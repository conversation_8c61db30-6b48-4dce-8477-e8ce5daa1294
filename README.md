# 🚀 Python to EXE Converter Pro v2.0

واجهة رسومية احترافية بتصميم زجاجي مدمج لتحويل ملفات Python إلى تطبيقات تنفيذية (.exe) باستخدام PyInstaller.

## ✨ المميزات الجديدة v2.0

### 🎨 التصميم المحسن
- **واجهة زجاجية مدمجة** بدون نوافذ منبثقة
- **تخطيط أفقي ذكي** يستغل كامل مساحة الشاشة
- **أزرار ديناميكية محسنة** مع تأثيرات تفاعلية متقدمة
- **ألوان متدرجة عصرية** مع نظام ألوان محسن
- **دعم اللغة العربية** بالكامل مع خطوط محسنة
- **بدون تمرير** - جميع العناصر مرئية في شاشة واحدة

### ⚙️ الوظائف المتقدمة
- **تحويل ملف واحد أو مجلد كامل**
- **خيارات متقدمة مدمجة**:
  - ملف واحد أو متعدد
  - إخفاء/إظهار الكونسول
  - وضع التصحيح
  - تحسين الحجم
  - ضغط UPX
  - إضافة أيقونة مخصصة

### 🔧 أدوات مساعدة مدمجة
- **معاينة الأمر المباشرة** في الواجهة الرئيسية
- **فحص المتطلبات** مع عرض النتائج في السجل
- **شريط تقدم** مع حالة التحويل المباشرة
- **سجل العمليات المباشر** بدون نوافذ منبثقة
- **حفظ واستعادة الإعدادات** تلقائياً

### 📊 المراقبة والتتبع المدمج
- **عمودين متوازيين**: إعدادات على اليسار، معاينة وسجل على اليمين
- **قائمة الملفات المحولة** في تبويبات مدمجة
- **سجل مفصل للعمليات** مع تمرير ذكي
- **رسائل حالة ديناميكية** في شريط الحالة
- **تسجيل الأخطاء والنجاح** مع طوابع زمنية

### 🚫 بدون نوافذ منبثقة
- **جميع العمليات مدمجة** في الواجهة الرئيسية
- **لا توجد نوافذ إضافية** تحتاج للإغلاق
- **تجربة مستخدم سلسة** بدون انقطاع
- **استغلال كامل للمساحة** المتاحة

## 🛠️ المتطلبات

### المتطلبات الأساسية
- Python 3.7 أو أحدث
- نظام التشغيل: Windows, macOS, Linux

### المكتبات المطلوبة
```bash
pip install PyInstaller
pip install Pillow  # اختياري لدعم الصور
```

## 🚀 التثبيت والتشغيل

### 1. تحميل الملفات
```bash
git clone [repository-url]
cd python-to-exe-converter
```

### 2. تثبيت المتطلبات (تلقائي)
التطبيق يقوم بتثبيت المكتبات المطلوبة تلقائياً عند التشغيل الأول.

### 3. تشغيل التطبيق
```bash
python pyinstaller_gui.py
```

## 📖 كيفية الاستخدام

### الخطوات الأساسية
1. **اختيار الملف المصدر**:
   - انقر على "📄 ملف" لاختيار ملف Python واحد
   - أو انقر على "📁 مجلد" لاختيار مجلد يحتوي على ملفات Python

2. **تحديد مجلد الحفظ**:
   - انقر على "📁 تصفح" لاختيار مجلد حفظ الملف التنفيذي

3. **ضبط الخيارات المتقدمة**:
   - **📦 ملف واحد**: إنشاء ملف exe واحد فقط
   - **🖥️ إخفاء الكونسول**: إخفاء نافذة سطر الأوامر
   - **🐛 وضع التصحيح**: تفعيل وضع التصحيح للأخطاء
   - **⚡ تحسين الحجم**: تقليل حجم الملف النهائي
   - **🗜️ ضغط UPX**: ضغط إضافي للملف
   - **🎨 أيقونة التطبيق**: إضافة أيقونة مخصصة

4. **معاينة الأمر** (اختياري):
   - انقر على "👁️ معاينة الأمر" لرؤية أمر PyInstaller الذي سيتم تنفيذه

5. **فحص المتطلبات** (اختياري):
   - انقر على "🔍 فحص المتطلبات" للتأكد من جاهزية النظام

6. **بدء التحويل**:
   - انقر على "🚀 بدء التحويل" لبدء عملية التحويل

### نصائح مهمة
- **تأكد من صحة الكود**: استخدم فحص المتطلبات للتأكد من عدم وجود أخطاء نحوية
- **اختر الخيارات المناسبة**: استخدم "ملف واحد" للتوزيع السهل
- **استخدم الأيقونات**: أضف أيقونة مخصصة لإعطاء مظهر احترافي
- **راقب السجل**: تابع سجل العمليات لمعرفة تفاصيل التحويل

## 🎯 الخيارات المتقدمة

### خيارات PyInstaller المدعومة
- `--onefile`: إنشاء ملف تنفيذي واحد
- `--noconsole`: إخفاء نافذة الكونسول
- `--debug=all`: تفعيل وضع التصحيح الشامل
- `--optimize=2`: تحسين الكود وتقليل الحجم
- `--icon`: إضافة أيقونة مخصصة
- `--distpath`: تحديد مجلد الحفظ
- `--upx-dir`: استخدام ضغط UPX

### ملفات الإعدادات
- `settings.json`: حفظ إعدادات المستخدم
- `conversion_log.txt`: سجل مفصل لجميع العمليات

## 🐛 حل المشاكل الشائعة

### مشكلة: "PyInstaller غير مثبت"
```bash
pip install PyInstaller
```

### مشكلة: "خطأ في بناء الجملة"
- تأكد من صحة كود Python
- استخدم فحص المتطلبات للتحقق

### مشكلة: "مساحة القرص غير كافية"
- تأكد من وجود مساحة كافية (1 جيجابايت على الأقل)
- احذف الملفات المؤقتة

### مشكلة: "فشل التحويل"
- تحقق من سجل العمليات للتفاصيل
- تأكد من عدم وجود مكتبات مفقودة
- جرب وضع التصحيح

## 📝 الترخيص

هذا المشروع مفتوح المصدر ومتاح للاستخدام الشخصي والتجاري.

## 🤝 المساهمة

نرحب بالمساهمات! يرجى:
1. عمل Fork للمشروع
2. إنشاء branch جديد للميزة
3. إرسال Pull Request

## 📞 الدعم

للحصول على الدعم أو الإبلاغ عن مشاكل، يرجى إنشاء Issue في المستودع.

---

**تم تطويره بـ ❤️ لمجتمع المطورين العرب**
