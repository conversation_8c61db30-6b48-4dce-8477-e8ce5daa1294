#!/usr/bin/env python3
"""
ملف التثبيت والتشغيل التلقائي
يقوم بتثبيت المتطلبات وتشغيل التطبيق
"""

import subprocess
import sys
import os

def install_requirements():
    """تثبيت المتطلبات"""
    print("🔧 تثبيت المتطلبات...")
    
    requirements = [
        "PyInstaller>=5.0.0",
        "Pillow>=9.0.0"
    ]
    
    for requirement in requirements:
        try:
            print(f"📦 تثبيت {requirement}...")
            subprocess.check_call([
                sys.executable, "-m", "pip", "install", requirement
            ])
            print(f"✅ تم تثبيت {requirement} بنجاح")
        except subprocess.CalledProcessError as e:
            print(f"❌ فشل تثبيت {requirement}: {e}")
            return False
    
    return True

def check_python_version():
    """فحص إصدار Python"""
    version = sys.version_info
    if version.major < 3 or (version.major == 3 and version.minor < 7):
        print("❌ يتطلب Python 3.7 أو أحدث")
        print(f"الإصدار الحالي: {version.major}.{version.minor}.{version.micro}")
        return False
    
    print(f"✅ Python {version.major}.{version.minor}.{version.micro}")
    return True

def run_application():
    """تشغيل التطبيق"""
    print("🚀 تشغيل التطبيق...")
    
    try:
        # تشغيل التطبيق الرئيسي
        subprocess.run([sys.executable, "pyinstaller_gui.py"])
    except FileNotFoundError:
        print("❌ لم يتم العثور على ملف pyinstaller_gui.py")
        return False
    except Exception as e:
        print(f"❌ خطأ في تشغيل التطبيق: {e}")
        return False
    
    return True

def main():
    """الوظيفة الرئيسية"""
    print("=" * 50)
    print("🚀 Python to EXE Converter Pro")
    print("=" * 50)
    
    # فحص إصدار Python
    if not check_python_version():
        input("اضغط Enter للخروج...")
        return
    
    # تثبيت المتطلبات
    if not install_requirements():
        print("❌ فشل في تثبيت المتطلبات")
        input("اضغط Enter للخروج...")
        return
    
    print("✅ تم تثبيت جميع المتطلبات بنجاح!")
    print("-" * 50)
    
    # تشغيل التطبيق
    run_application()

if __name__ == "__main__":
    main()
