# ملف تخصيص الثيم الزجاجي
import tkinter as tk
from tkinter import ttk

class GlassTheme:
    """كلاس لتطبيق الثيم الزجاجي على العناصر"""
    
    @staticmethod
    def apply_glass_effect(widget, bg_color='#0f3460', border_color='#533483'):
        """تطبيق التأثير الزجاجي على العنصر"""
        widget.configure(
            bg=bg_color,
            relief='flat',
            bd=1,
            highlightbackground=border_color,
            highlightthickness=1
        )
    
    @staticmethod
    def create_gradient_frame(parent, color1='#1a1a2e', color2='#16213e'):
        """إنشاء إطار بتدرج لوني"""
        frame = tk.Frame(parent)
        canvas = tk.Canvas(frame, highlightthickness=0)
        canvas.pack(fill='both', expand=True)
        
        # رسم التدرج
        def draw_gradient(event=None):
            canvas.delete("gradient")
            width = canvas.winfo_width()
            height = canvas.winfo_height()
            
            # تحويل الألوان إلى RGB
            r1, g1, b1 = int(color1[1:3], 16), int(color1[3:5], 16), int(color1[5:7], 16)
            r2, g2, b2 = int(color2[1:3], 16), int(color2[3:5], 16), int(color2[5:7], 16)
            
            # رسم خطوط التدرج
            for i in range(height):
                ratio = i / height
                r = int(r1 + (r2 - r1) * ratio)
                g = int(g1 + (g2 - g1) * ratio)
                b = int(b1 + (b2 - b1) * ratio)
                color = f"#{r:02x}{g:02x}{b:02x}"
                canvas.create_line(0, i, width, i, fill=color, tags="gradient")
        
        canvas.bind('<Configure>', draw_gradient)
        return frame
    
    @staticmethod
    def animate_button_hover(button, normal_color='#e94560', hover_color='#ff6b8a'):
        """إضافة تأثير الحركة للأزرار"""
        def on_enter(e):
            button.configure(bg=hover_color)
            # تأثير التكبير
            button.configure(font=('Segoe UI', 11, 'bold'))
        
        def on_leave(e):
            button.configure(bg=normal_color)
            button.configure(font=('Segoe UI', 10, 'bold'))
        
        button.bind('<Enter>', on_enter)
        button.bind('<Leave>', on_leave)
    
    @staticmethod
    def create_glowing_border(widget, glow_color='#e94560'):
        """إنشاء حدود متوهجة"""
        def on_focus_in(event):
            widget.configure(highlightbackground=glow_color, highlightthickness=2)
        
        def on_focus_out(event):
            widget.configure(highlightbackground='#533483', highlightthickness=1)
        
        widget.bind('<FocusIn>', on_focus_in)
        widget.bind('<FocusOut>', on_focus_out)
    
    @staticmethod
    def create_floating_effect(widget):
        """إنشاء تأثير الطفو"""
        original_relief = widget.cget('relief')
        
        def on_enter(event):
            widget.configure(relief='raised', bd=2)
        
        def on_leave(event):
            widget.configure(relief=original_relief, bd=1)
        
        widget.bind('<Enter>', on_enter)
        widget.bind('<Leave>', on_leave)
    
    @staticmethod
    def setup_ttk_styles():
        """إعداد أنماط ttk"""
        style = ttk.Style()
        
        # تخصيص شريط التقدم
        style.configure(
            'Glass.Horizontal.TProgressbar',
            background='#e94560',
            troughcolor='#16213e',
            borderwidth=0,
            lightcolor='#e94560',
            darkcolor='#e94560'
        )
        
        # تخصيص التبويبات
        style.configure(
            'Glass.TNotebook',
            background='#0f3460',
            borderwidth=0
        )
        
        style.configure(
            'Glass.TNotebook.Tab',
            background='#16213e',
            foreground='#ffffff',
            padding=[20, 10],
            borderwidth=0
        )
        
        style.map(
            'Glass.TNotebook.Tab',
            background=[('selected', '#e94560'), ('active', '#533483')]
        )
        
        # تخصيص شريط التمرير
        style.configure(
            'Glass.Vertical.TScrollbar',
            background='#16213e',
            troughcolor='#0f3460',
            borderwidth=0,
            arrowcolor='#ffffff'
        )
