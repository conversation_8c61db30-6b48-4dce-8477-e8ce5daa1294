import os
import subprocess
import sys
import tkinter as tk
from tkinter import filedialog, messagebox, ttk
import traceback
import threading
import json
import logging
from datetime import datetime
import shutil
from pathlib import Path

# إعداد نظام السجلات
logging.basicConfig(
    filename='conversion_log.txt',
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    encoding='utf-8'
)

# وظيفة لتثبيت المكتبات تلقائياً إذا لم تكن مثبتة
def install_package(package):
    try:
        __import__(package)
    except ImportError:
        print(f"Installing {package}...")
        subprocess.check_call([sys.executable, "-m", "pip", "install", package])

# تثبيت المكتبات المطلوبة
required_packages = ["PyInstaller", "Pillow"]
for package in required_packages:
    install_package(package)

# الآن استيرادها
import PyInstaller.__main__
try:
    from PIL import Image, ImageTk
    PIL_AVAILABLE = True
except ImportError:
    PIL_AVAILABLE = False

# ألوان وتصميم الواجهة الزجاجية
COLORS = {
    'bg_primary': '#1a1a2e',
    'bg_secondary': '#16213e',
    'bg_glass': '#0f3460',
    'accent': '#e94560',
    'accent_hover': '#ff6b8a',
    'text_primary': '#ffffff',
    'text_secondary': '#b8c6db',
    'success': '#00d4aa',
    'warning': '#ffa726',
    'error': '#ef5350',
    'glass_border': '#533483'
}

class GlassFrame(tk.Frame):
    """إطار زجاجي مخصص"""
    def __init__(self, parent, **kwargs):
        super().__init__(parent, **kwargs)
        self.configure(
            bg=COLORS['bg_glass'],
            relief='flat',
            bd=1,
            highlightbackground=COLORS['glass_border'],
            highlightthickness=1
        )

class AnimatedButton(tk.Button):
    """زر متحرك مخصص"""
    def __init__(self, parent, **kwargs):
        super().__init__(parent, **kwargs)
        self.configure(
            bg=COLORS['accent'],
            fg=COLORS['text_primary'],
            relief='flat',
            bd=0,
            font=('Segoe UI', 10, 'bold'),
            cursor='hand2',
            activebackground=COLORS['accent_hover'],
            activeforeground=COLORS['text_primary']
        )
        self.bind('<Enter>', self.on_enter)
        self.bind('<Leave>', self.on_leave)

    def on_enter(self, e):
        self.configure(bg=COLORS['accent_hover'])

    def on_leave(self, e):
        self.configure(bg=COLORS['accent'])

class PyInstallerGUI:
    def __init__(self):
        self.root = tk.Tk()
        self.setup_window()
        self.setup_variables()
        self.create_widgets()
        self.load_settings()

    def setup_window(self):
        """إعداد النافذة الرئيسية"""
        self.root.title("🚀 Python to EXE Converter Pro")
        self.root.geometry("900x700")
        self.root.minsize(800, 600)
        self.root.configure(bg=COLORS['bg_primary'])

        # جعل النافذة في المنتصف
        self.center_window()

        # إعداد الأيقونة إذا كانت متوفرة
        try:
            self.root.iconbitmap('icon.ico')
        except:
            pass

    def center_window(self):
        """وضع النافذة في منتصف الشاشة"""
        self.root.update_idletasks()
        width = self.root.winfo_width()
        height = self.root.winfo_height()
        x = (self.root.winfo_screenwidth() // 2) - (width // 2)
        y = (self.root.winfo_screenheight() // 2) - (height // 2)
        self.root.geometry(f'{width}x{height}+{x}+{y}')

    def setup_variables(self):
        """إعداد المتغيرات"""
        self.selected_path = tk.StringVar()
        self.output_folder_var = tk.StringVar()
        self.icon_path = tk.StringVar()

        # خيارات التحويل
        self.one_file = tk.BooleanVar(value=True)
        self.hide_console = tk.BooleanVar(value=True)
        self.debug_mode = tk.BooleanVar()
        self.add_data = tk.BooleanVar()
        self.optimize = tk.BooleanVar(value=True)
        self.upx_compress = tk.BooleanVar()

        # حالة التطبيق
        self.is_converting = False

    def create_widgets(self):
        """إنشاء عناصر الواجهة"""
        # إنشاء الإطار الرئيسي مع تمرير
        main_canvas = tk.Canvas(self.root, bg=COLORS['bg_primary'], highlightthickness=0)
        scrollbar = ttk.Scrollbar(self.root, orient="vertical", command=main_canvas.yview)
        self.scrollable_frame = tk.Frame(main_canvas, bg=COLORS['bg_primary'])

        self.scrollable_frame.bind(
            "<Configure>",
            lambda e: main_canvas.configure(scrollregion=main_canvas.bbox("all"))
        )

        main_canvas.create_window((0, 0), window=self.scrollable_frame, anchor="nw")
        main_canvas.configure(yscrollcommand=scrollbar.set)

        main_canvas.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")

        # العنوان الرئيسي
        self.create_header()

        # قسم اختيار الملفات
        self.create_file_selection_section()

        # قسم الخيارات المتقدمة
        self.create_advanced_options_section()

        # قسم معاينة الأمر
        self.create_command_preview_section()

        # قسم التحويل والتقدم
        self.create_conversion_section()

        # قسم السجل والملفات المحولة
        self.create_log_section()

        # شريط الحالة
        self.create_status_bar()

    def create_header(self):
        """إنشاء العنوان الرئيسي"""
        header_frame = GlassFrame(self.scrollable_frame)
        header_frame.pack(fill='x', padx=20, pady=(20, 10))

        title_label = tk.Label(
            header_frame,
            text="🚀 Python to EXE Converter Pro",
            font=('Segoe UI', 24, 'bold'),
            fg=COLORS['text_primary'],
            bg=COLORS['bg_glass']
        )
        title_label.pack(pady=20)

        subtitle_label = tk.Label(
            header_frame,
            text="تحويل ملفات Python إلى تطبيقات تنفيذية بواجهة احترافية",
            font=('Segoe UI', 12),
            fg=COLORS['text_secondary'],
            bg=COLORS['bg_glass']
        )
        subtitle_label.pack(pady=(0, 20))

    def create_file_selection_section(self):
        """قسم اختيار الملفات"""
        file_frame = GlassFrame(self.scrollable_frame)
        file_frame.pack(fill='x', padx=20, pady=10)

        # عنوان القسم
        section_title = tk.Label(
            file_frame,
            text="📁 اختيار الملفات",
            font=('Segoe UI', 16, 'bold'),
            fg=COLORS['text_primary'],
            bg=COLORS['bg_glass']
        )
        section_title.pack(pady=(15, 10))

        # إطار اختيار الملف المصدر
        source_frame = tk.Frame(file_frame, bg=COLORS['bg_glass'])
        source_frame.pack(fill='x', padx=20, pady=10)

        tk.Label(
            source_frame,
            text="الملف المصدر:",
            font=('Segoe UI', 11, 'bold'),
            fg=COLORS['text_primary'],
            bg=COLORS['bg_glass']
        ).pack(anchor='w')

        source_entry_frame = tk.Frame(source_frame, bg=COLORS['bg_glass'])
        source_entry_frame.pack(fill='x', pady=(5, 0))

        self.source_entry = tk.Entry(
            source_entry_frame,
            textvariable=self.selected_path,
            font=('Segoe UI', 10),
            bg=COLORS['bg_secondary'],
            fg=COLORS['text_primary'],
            relief='flat',
            bd=5
        )
        self.source_entry.pack(side='left', fill='x', expand=True, padx=(0, 10))

        AnimatedButton(
            source_entry_frame,
            text="📄 ملف",
            command=self.browse_file,
            width=8
        ).pack(side='right', padx=(0, 5))

        AnimatedButton(
            source_entry_frame,
            text="📁 مجلد",
            command=self.browse_folder,
            width=8
        ).pack(side='right')

        # إطار مجلد الحفظ
        output_frame = tk.Frame(file_frame, bg=COLORS['bg_glass'])
        output_frame.pack(fill='x', padx=20, pady=10)

        tk.Label(
            output_frame,
            text="مجلد الحفظ:",
            font=('Segoe UI', 11, 'bold'),
            fg=COLORS['text_primary'],
            bg=COLORS['bg_glass']
        ).pack(anchor='w')

        output_entry_frame = tk.Frame(output_frame, bg=COLORS['bg_glass'])
        output_entry_frame.pack(fill='x', pady=(5, 15))

        self.output_entry = tk.Entry(
            output_entry_frame,
            textvariable=self.output_folder_var,
            font=('Segoe UI', 10),
            bg=COLORS['bg_secondary'],
            fg=COLORS['text_primary'],
            relief='flat',
            bd=5
        )
        self.output_entry.pack(side='left', fill='x', expand=True, padx=(0, 10))

        AnimatedButton(
            output_entry_frame,
            text="📁 تصفح",
            command=self.browse_output_folder,
            width=10
        ).pack(side='right')

    def browse_file(self):
        """تصفح ملف Python"""
        file_path = filedialog.askopenfilename(
            title="اختر ملف Python",
            filetypes=[("Python Files", "*.py"), ("All Files", "*.*")]
        )
        if file_path:
            self.selected_path.set(file_path)
            self.update_command_preview()

    def browse_folder(self):
        """تصفح مجلد"""
        folder_path = filedialog.askdirectory(title="اختر مجلد Python")
        if folder_path:
            self.selected_path.set(folder_path)
            self.update_command_preview()

    def browse_output_folder(self):
        """تصفح مجلد الحفظ"""
        folder_path = filedialog.askdirectory(title="اختر مجلد الحفظ")
        if folder_path:
            self.output_folder_var.set(folder_path)
            self.update_command_preview()

    def create_advanced_options_section(self):
        """قسم الخيارات المتقدمة"""
        options_frame = GlassFrame(self.scrollable_frame)
        options_frame.pack(fill='x', padx=20, pady=10)

        # عنوان القسم
        section_title = tk.Label(
            options_frame,
            text="⚙️ الخيارات المتقدمة",
            font=('Segoe UI', 16, 'bold'),
            fg=COLORS['text_primary'],
            bg=COLORS['bg_glass']
        )
        section_title.pack(pady=(15, 10))

        # إطار الخيارات الأساسية
        basic_options_frame = tk.Frame(options_frame, bg=COLORS['bg_glass'])
        basic_options_frame.pack(fill='x', padx=20, pady=10)

        # الصف الأول من الخيارات
        row1 = tk.Frame(basic_options_frame, bg=COLORS['bg_glass'])
        row1.pack(fill='x', pady=5)

        self.create_checkbox(row1, "📦 ملف واحد", self.one_file, "إنشاء ملف exe واحد").pack(side='left', padx=(0, 20))
        self.create_checkbox(row1, "🖥️ إخفاء الكونسول", self.hide_console, "إخفاء نافذة سطر الأوامر").pack(side='left', padx=(0, 20))

        # الصف الثاني من الخيارات
        row2 = tk.Frame(basic_options_frame, bg=COLORS['bg_glass'])
        row2.pack(fill='x', pady=5)

        self.create_checkbox(row2, "🐛 وضع التصحيح", self.debug_mode, "تفعيل وضع التصحيح").pack(side='left', padx=(0, 20))
        self.create_checkbox(row2, "⚡ تحسين الحجم", self.optimize, "تحسين حجم الملف").pack(side='left', padx=(0, 20))

        # الصف الثالث من الخيارات
        row3 = tk.Frame(basic_options_frame, bg=COLORS['bg_glass'])
        row3.pack(fill='x', pady=5)

        self.create_checkbox(row3, "📁 إضافة ملفات", self.add_data, "إضافة ملفات إضافية").pack(side='left', padx=(0, 20))
        self.create_checkbox(row3, "🗜️ ضغط UPX", self.upx_compress, "ضغط الملف باستخدام UPX").pack(side='left', padx=(0, 20))

        # إطار الأيقونة
        icon_frame = tk.Frame(options_frame, bg=COLORS['bg_glass'])
        icon_frame.pack(fill='x', padx=20, pady=10)

        tk.Label(
            icon_frame,
            text="🎨 أيقونة التطبيق:",
            font=('Segoe UI', 11, 'bold'),
            fg=COLORS['text_primary'],
            bg=COLORS['bg_glass']
        ).pack(anchor='w')

        icon_entry_frame = tk.Frame(icon_frame, bg=COLORS['bg_glass'])
        icon_entry_frame.pack(fill='x', pady=(5, 15))

        self.icon_entry = tk.Entry(
            icon_entry_frame,
            textvariable=self.icon_path,
            font=('Segoe UI', 10),
            bg=COLORS['bg_secondary'],
            fg=COLORS['text_primary'],
            relief='flat',
            bd=5
        )
        self.icon_entry.pack(side='left', fill='x', expand=True, padx=(0, 10))

        AnimatedButton(
            icon_entry_frame,
            text="🎨 تصفح",
            command=self.browse_icon,
            width=10
        ).pack(side='right')

    def create_checkbox(self, parent, text, variable, tooltip=""):
        """إنشاء checkbox مخصص"""
        frame = tk.Frame(parent, bg=COLORS['bg_glass'])

        checkbox = tk.Checkbutton(
            frame,
            text=text,
            variable=variable,
            font=('Segoe UI', 10),
            fg=COLORS['text_primary'],
            bg=COLORS['bg_glass'],
            selectcolor=COLORS['bg_secondary'],
            activebackground=COLORS['bg_glass'],
            activeforeground=COLORS['text_primary'],
            command=self.update_command_preview
        )
        checkbox.pack()

        # إضافة tooltip إذا كان متوفراً
        if tooltip:
            self.create_tooltip(checkbox, tooltip)

        return frame

    def create_tooltip(self, widget, text):
        """إنشاء tooltip للعنصر"""
        def on_enter(event):
            tooltip = tk.Toplevel()
            tooltip.wm_overrideredirect(True)
            tooltip.wm_geometry(f"+{event.x_root+10}+{event.y_root+10}")

            label = tk.Label(
                tooltip,
                text=text,
                background=COLORS['bg_secondary'],
                foreground=COLORS['text_primary'],
                relief='solid',
                borderwidth=1,
                font=('Segoe UI', 9)
            )
            label.pack()

            widget.tooltip = tooltip

        def on_leave(event):
            if hasattr(widget, 'tooltip'):
                widget.tooltip.destroy()
                del widget.tooltip

        widget.bind('<Enter>', on_enter)
        widget.bind('<Leave>', on_leave)

    def browse_icon(self):
        """تصفح أيقونة"""
        icon_file = filedialog.askopenfilename(
            title="اختر أيقونة",
            filetypes=[
                ("Icon Files", "*.ico"),
                ("PNG Files", "*.png"),
                ("All Files", "*.*")
            ]
        )
        if icon_file:
            self.icon_path.set(icon_file)
            self.update_command_preview()

    def create_command_preview_section(self):
        """قسم معاينة الأمر"""
        preview_frame = GlassFrame(self.scrollable_frame)
        preview_frame.pack(fill='x', padx=20, pady=10)

        # عنوان القسم
        section_title = tk.Label(
            preview_frame,
            text="👁️ معاينة الأمر",
            font=('Segoe UI', 16, 'bold'),
            fg=COLORS['text_primary'],
            bg=COLORS['bg_glass']
        )
        section_title.pack(pady=(15, 10))

        # منطقة النص
        self.command_text = tk.Text(
            preview_frame,
            height=4,
            font=('Consolas', 9),
            bg=COLORS['bg_secondary'],
            fg=COLORS['text_primary'],
            relief='flat',
            bd=5,
            wrap=tk.WORD,
            state='disabled'
        )
        self.command_text.pack(fill='x', padx=20, pady=(0, 15))

    def create_conversion_section(self):
        """قسم التحويل والتقدم"""
        conversion_frame = GlassFrame(self.scrollable_frame)
        conversion_frame.pack(fill='x', padx=20, pady=10)

        # عنوان القسم
        section_title = tk.Label(
            conversion_frame,
            text="🚀 التحويل",
            font=('Segoe UI', 16, 'bold'),
            fg=COLORS['text_primary'],
            bg=COLORS['bg_glass']
        )
        section_title.pack(pady=(15, 10))

        # أزرار التحكم
        buttons_frame = tk.Frame(conversion_frame, bg=COLORS['bg_glass'])
        buttons_frame.pack(pady=10)

        self.convert_btn = AnimatedButton(
            buttons_frame,
            text="🚀 بدء التحويل",
            command=self.start_conversion,
            font=('Segoe UI', 12, 'bold'),
            width=15
        )
        self.convert_btn.pack(side='left', padx=10)

        self.check_btn = AnimatedButton(
            buttons_frame,
            text="🔍 فحص المتطلبات",
            command=self.check_requirements,
            width=15
        )
        self.check_btn.pack(side='left', padx=10)

        self.preview_btn = AnimatedButton(
            buttons_frame,
            text="👁️ معاينة الأمر",
            command=self.show_command_preview,
            width=15
        )
        self.preview_btn.pack(side='left', padx=10)

        # شريط التقدم
        progress_frame = tk.Frame(conversion_frame, bg=COLORS['bg_glass'])
        progress_frame.pack(fill='x', padx=20, pady=10)

        self.progress_bar = ttk.Progressbar(
            progress_frame,
            mode='indeterminate',
            style='Custom.Horizontal.TProgressbar'
        )
        self.progress_bar.pack(fill='x', pady=5)

        # تخصيص شريط التقدم
        style = ttk.Style()
        style.configure(
            'Custom.Horizontal.TProgressbar',
            background=COLORS['accent'],
            troughcolor=COLORS['bg_secondary'],
            borderwidth=0,
            lightcolor=COLORS['accent'],
            darkcolor=COLORS['accent']
        )

        # تسمية الحالة
        self.status_label = tk.Label(
            progress_frame,
            text="جاهز للتحويل",
            font=('Segoe UI', 11),
            fg=COLORS['success'],
            bg=COLORS['bg_glass']
        )
        self.status_label.pack(pady=5)

    def create_log_section(self):
        """قسم السجل والملفات المحولة"""
        log_frame = GlassFrame(self.scrollable_frame)
        log_frame.pack(fill='both', expand=True, padx=20, pady=10)

        # عنوان القسم
        section_title = tk.Label(
            log_frame,
            text="📋 السجل والملفات المحولة",
            font=('Segoe UI', 16, 'bold'),
            fg=COLORS['text_primary'],
            bg=COLORS['bg_glass']
        )
        section_title.pack(pady=(15, 10))

        # إطار التبويبات
        notebook = ttk.Notebook(log_frame)
        notebook.pack(fill='both', expand=True, padx=20, pady=(0, 15))

        # تبويب الملفات المحولة
        converted_frame = tk.Frame(notebook, bg=COLORS['bg_secondary'])
        notebook.add(converted_frame, text="الملفات المحولة")

        self.converted_listbox = tk.Listbox(
            converted_frame,
            font=('Segoe UI', 10),
            bg=COLORS['bg_secondary'],
            fg=COLORS['text_primary'],
            selectbackground=COLORS['accent'],
            relief='flat',
            bd=0
        )
        self.converted_listbox.pack(fill='both', expand=True, padx=10, pady=10)

        # تبويب السجل
        log_text_frame = tk.Frame(notebook, bg=COLORS['bg_secondary'])
        notebook.add(log_text_frame, text="سجل العمليات")

        self.log_text = tk.Text(
            log_text_frame,
            font=('Consolas', 9),
            bg=COLORS['bg_secondary'],
            fg=COLORS['text_primary'],
            relief='flat',
            bd=0,
            wrap=tk.WORD,
            state='disabled'
        )
        self.log_text.pack(fill='both', expand=True, padx=10, pady=10)

        # شريط تمرير للسجل
        log_scrollbar = ttk.Scrollbar(log_text_frame, orient="vertical", command=self.log_text.yview)
        self.log_text.configure(yscrollcommand=log_scrollbar.set)
        log_scrollbar.pack(side="right", fill="y")

    def create_status_bar(self):
        """شريط الحالة"""
        status_frame = tk.Frame(self.root, bg=COLORS['bg_secondary'], height=30)
        status_frame.pack(fill='x', side='bottom')
        status_frame.pack_propagate(False)

        self.status_bar_label = tk.Label(
            status_frame,
            text="جاهز | Python to EXE Converter Pro v2.0",
            font=('Segoe UI', 9),
            fg=COLORS['text_secondary'],
            bg=COLORS['bg_secondary']
        )
        self.status_bar_label.pack(side='left', padx=10, pady=5)
    def update_command_preview(self):
        """تحديث معاينة الأمر"""
        path = self.selected_path.get()
        if not path:
            self.command_text.config(state='normal')
            self.command_text.delete(1.0, tk.END)
            self.command_text.insert(tk.END, "اختر ملف Python أولاً...")
            self.command_text.config(state='disabled')
            return

        cmd_args = ['pyinstaller']

        if self.one_file.get():
            cmd_args.append('--onefile')

        if self.hide_console.get():
            cmd_args.append('--noconsole')

        if self.debug_mode.get():
            cmd_args.append('--debug=all')

        if self.optimize.get():
            cmd_args.append('--optimize=2')

        if self.upx_compress.get():
            cmd_args.append('--upx-dir=upx')

        if self.icon_path.get():
            cmd_args.extend(['--icon', f'"{self.icon_path.get()}"'])

        if self.output_folder_var.get():
            cmd_args.extend(['--distpath', f'"{self.output_folder_var.get()}"'])

        cmd_args.append(f'"{path}"')

        command_text = ' '.join(cmd_args)

        self.command_text.config(state='normal')
        self.command_text.delete(1.0, tk.END)
        self.command_text.insert(tk.END, command_text)
        self.command_text.config(state='disabled')

    def show_command_preview(self):
        """عرض معاينة الأمر في نافذة منفصلة"""
        preview_window = tk.Toplevel(self.root)
        preview_window.title("معاينة الأمر")
        preview_window.geometry("700x400")
        preview_window.configure(bg=COLORS['bg_primary'])
        preview_window.transient(self.root)
        preview_window.grab_set()

        # عنوان النافذة
        title_label = tk.Label(
            preview_window,
            text="👁️ معاينة أمر PyInstaller",
            font=('Segoe UI', 16, 'bold'),
            fg=COLORS['text_primary'],
            bg=COLORS['bg_primary']
        )
        title_label.pack(pady=20)

        # منطقة النص
        text_frame = GlassFrame(preview_window)
        text_frame.pack(fill='both', expand=True, padx=20, pady=(0, 20))

        text_widget = tk.Text(
            text_frame,
            font=('Consolas', 11),
            bg=COLORS['bg_secondary'],
            fg=COLORS['text_primary'],
            relief='flat',
            bd=10,
            wrap=tk.WORD
        )
        text_widget.pack(fill='both', expand=True, padx=10, pady=10)

        # إدراج الأمر
        path = self.selected_path.get()
        if path:
            cmd_args = self.build_command_args(path)
            command_text = ' '.join(cmd_args)
            text_widget.insert(tk.END, command_text)
        else:
            text_widget.insert(tk.END, "اختر ملف Python أولاً...")

        # زر النسخ
        copy_btn = AnimatedButton(
            preview_window,
            text="📋 نسخ الأمر",
            command=lambda: self.copy_to_clipboard(text_widget.get(1.0, tk.END).strip())
        )
        copy_btn.pack(pady=10)

    def copy_to_clipboard(self, text):
        """نسخ النص إلى الحافظة"""
        self.root.clipboard_clear()
        self.root.clipboard_append(text)
        self.update_status("تم نسخ الأمر إلى الحافظة")

    def build_command_args(self, path):
        """بناء قائمة أوامر PyInstaller"""
        cmd_args = ['pyinstaller']

        if self.one_file.get():
            cmd_args.append('--onefile')

        if self.hide_console.get():
            cmd_args.append('--noconsole')

        if self.debug_mode.get():
            cmd_args.append('--debug=all')

        if self.optimize.get():
            cmd_args.append('--optimize=2')

        if self.upx_compress.get():
            cmd_args.append('--upx-dir=upx')

        if self.icon_path.get():
            cmd_args.extend(['--icon', self.icon_path.get()])

        if self.output_folder_var.get():
            cmd_args.extend(['--distpath', self.output_folder_var.get()])

        cmd_args.append(path)

        return cmd_args

    def check_requirements(self):
        """فحص المتطلبات"""
        issues = []
        warnings = []

        # تحقق من وجود Python
        if not sys.executable:
            issues.append("❌ لم يتم العثور على Python")
        else:
            warnings.append(f"✅ Python: {sys.version}")

        # تحقق من PyInstaller
        try:
            import PyInstaller
            warnings.append(f"✅ PyInstaller: {PyInstaller.__version__}")
        except ImportError:
            issues.append("❌ PyInstaller غير مثبت")

        # تحقق من مساحة القرص
        try:
            free_space = shutil.disk_usage('.').free / (1024**3)  # GB
            if free_space < 1:
                issues.append(f"⚠️ مساحة القرص أقل من 1 جيجابايت ({free_space:.1f} GB)")
            else:
                warnings.append(f"✅ مساحة القرص: {free_space:.1f} GB")
        except:
            warnings.append("⚠️ لا يمكن فحص مساحة القرص")

        # تحقق من صحة الملف
        path = self.selected_path.get()
        if path and os.path.isfile(path):
            try:
                with open(path, 'r', encoding='utf-8') as f:
                    compile(f.read(), path, 'exec')
                warnings.append("✅ الملف صحيح نحوياً")
            except SyntaxError as e:
                issues.append(f"❌ خطأ في بناء الجملة: {e}")
            except Exception as e:
                warnings.append(f"⚠️ تحذير: {e}")
        elif path:
            warnings.append("ℹ️ سيتم فحص جميع ملفات Python في المجلد")

        # عرض النتائج
        self.show_requirements_result(issues, warnings)

    def show_requirements_result(self, issues, warnings):
        """عرض نتائج فحص المتطلبات"""
        result_window = tk.Toplevel(self.root)
        result_window.title("نتائج فحص المتطلبات")
        result_window.geometry("600x500")
        result_window.configure(bg=COLORS['bg_primary'])
        result_window.transient(self.root)
        result_window.grab_set()

        # عنوان النافذة
        title_label = tk.Label(
            result_window,
            text="🔍 نتائج فحص المتطلبات",
            font=('Segoe UI', 16, 'bold'),
            fg=COLORS['text_primary'],
            bg=COLORS['bg_primary']
        )
        title_label.pack(pady=20)

        # منطقة النتائج
        result_frame = GlassFrame(result_window)
        result_frame.pack(fill='both', expand=True, padx=20, pady=(0, 20))

        result_text = tk.Text(
            result_frame,
            font=('Segoe UI', 11),
            bg=COLORS['bg_secondary'],
            fg=COLORS['text_primary'],
            relief='flat',
            bd=10,
            wrap=tk.WORD
        )
        result_text.pack(fill='both', expand=True, padx=10, pady=10)

        # إدراج النتائج
        if issues:
            result_text.insert(tk.END, "🚨 مشاكل يجب حلها:\n\n")
            for issue in issues:
                result_text.insert(tk.END, f"{issue}\n")
            result_text.insert(tk.END, "\n")

        if warnings:
            result_text.insert(tk.END, "ℹ️ معلومات النظام:\n\n")
            for warning in warnings:
                result_text.insert(tk.END, f"{warning}\n")

        if not issues:
            result_text.insert(tk.END, "\n🎉 جميع المتطلبات متوفرة! يمكنك البدء في التحويل.")

        result_text.config(state='disabled')
    def start_conversion(self):
        """بدء عملية التحويل"""
        if self.is_converting:
            messagebox.showwarning("تحذير", "عملية تحويل جارية بالفعل")
            return

        path = self.selected_path.get()
        if not path:
            messagebox.showerror("خطأ", "من فضلك اختر ملف أو مجلد أولاً")
            return

        output_folder = self.output_folder_var.get()
        if not output_folder:
            output_folder = filedialog.askdirectory(title="اختر مجلد الحفظ")
            if not output_folder:
                return
            self.output_folder_var.set(output_folder)

        # بدء التحويل في thread منفصل
        self.is_converting = True
        self.convert_btn.configure(text="⏳ جاري التحويل...", state="disabled")
        self.progress_bar.start()
        self.update_status("جاري التحويل...", COLORS['warning'])

        thread = threading.Thread(target=self.conversion_worker, args=(path, output_folder))
        thread.daemon = True
        thread.start()

    def conversion_worker(self, path, output_folder):
        """عامل التحويل في thread منفصل"""
        try:
            if os.path.isfile(path):
                # تحويل ملف واحد
                self.convert_single_file(path, output_folder)
            else:
                # تحويل جميع ملفات .py في المجلد
                self.convert_folder(path, output_folder)

            # تسجيل النجاح
            self.log_conversion(path, success=True)

            # تحديث الواجهة
            self.root.after(0, self.conversion_success)

        except Exception as e:
            error_msg = str(e)
            self.log_conversion(path, success=False, error_msg=error_msg)

            # تحديث الواجهة
            self.root.after(0, lambda: self.conversion_error(error_msg))

    def convert_single_file(self, file_path, output_folder):
        """تحويل ملف واحد"""
        cmd_args = self.build_command_args(file_path)

        # تشغيل PyInstaller
        PyInstaller.__main__.run(cmd_args[1:])  # إزالة 'pyinstaller' من البداية

        # إضافة إلى قائمة الملفات المحولة
        filename = os.path.basename(file_path)
        timestamp = datetime.now().strftime("%H:%M:%S")
        self.root.after(0, lambda: self.add_to_converted_list(f"{filename} - {timestamp}"))

    def convert_folder(self, folder_path, output_folder):
        """تحويل جميع ملفات Python في المجلد"""
        py_files = [os.path.join(folder_path, f) for f in os.listdir(folder_path) if f.endswith('.py')]

        if not py_files:
            raise Exception("المجلد لا يحتوي على ملفات Python")

        for file_path in py_files:
            self.convert_single_file(file_path, output_folder)

    def conversion_success(self):
        """تحديث الواجهة عند نجاح التحويل"""
        self.is_converting = False
        self.convert_btn.configure(text="🚀 بدء التحويل", state="normal")
        self.progress_bar.stop()
        self.update_status("تم التحويل بنجاح!", COLORS['success'])

        output_folder = self.output_folder_var.get()
        messagebox.showinfo("نجاح", f"تم تحويل الملف بنجاح إلى:\n{output_folder}")

        # حفظ الإعدادات
        self.save_settings()

    def conversion_error(self, error_msg):
        """تحديث الواجهة عند فشل التحويل"""
        self.is_converting = False
        self.convert_btn.configure(text="🚀 بدء التحويل", state="normal")
        self.progress_bar.stop()
        self.update_status("فشل التحويل", COLORS['error'])

        messagebox.showerror("خطأ أثناء التحويل", f"فشل التحويل:\n{error_msg}")

    def log_conversion(self, file_path, success=True, error_msg=None):
        """تسجيل عملية التحويل"""
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")

        if success:
            message = f"[{timestamp}] ✅ تم تحويل {file_path} بنجاح"
            logging.info(f"تم تحويل {file_path} بنجاح")
        else:
            message = f"[{timestamp}] ❌ فشل تحويل {file_path}: {error_msg}"
            logging.error(f"فشل تحويل {file_path}: {error_msg}")

        # إضافة إلى سجل الواجهة
        self.root.after(0, lambda: self.add_to_log(message))

    def add_to_log(self, message):
        """إضافة رسالة إلى سجل الواجهة"""
        self.log_text.config(state='normal')
        self.log_text.insert(tk.END, message + "\n")
        self.log_text.see(tk.END)
        self.log_text.config(state='disabled')

    def add_to_converted_list(self, item):
        """إضافة عنصر إلى قائمة الملفات المحولة"""
        self.converted_listbox.insert(tk.END, item)
        self.converted_listbox.see(tk.END)

    def update_status(self, message, color=None):
        """تحديث رسالة الحالة"""
        if color is None:
            color = COLORS['text_primary']

        self.status_label.configure(text=message, fg=color)
        self.status_bar_label.configure(text=f"{message} | Python to EXE Converter Pro v2.0")

    def save_settings(self):
        """حفظ الإعدادات"""
        settings = {
            'last_path': self.selected_path.get(),
            'output_folder': self.output_folder_var.get(),
            'icon_path': self.icon_path.get(),
            'one_file': self.one_file.get(),
            'hide_console': self.hide_console.get(),
            'debug_mode': self.debug_mode.get(),
            'add_data': self.add_data.get(),
            'optimize': self.optimize.get(),
            'upx_compress': self.upx_compress.get()
        }

        try:
            with open('settings.json', 'w', encoding='utf-8') as f:
                json.dump(settings, f, ensure_ascii=False, indent=2)
        except Exception as e:
            print(f"خطأ في حفظ الإعدادات: {e}")

    def load_settings(self):
        """تحميل الإعدادات"""
        try:
            if os.path.exists('settings.json'):
                with open('settings.json', 'r', encoding='utf-8') as f:
                    settings = json.load(f)

                    self.selected_path.set(settings.get('last_path', ''))
                    self.output_folder_var.set(settings.get('output_folder', ''))
                    self.icon_path.set(settings.get('icon_path', ''))
                    self.one_file.set(settings.get('one_file', True))
                    self.hide_console.set(settings.get('hide_console', True))
                    self.debug_mode.set(settings.get('debug_mode', False))
                    self.add_data.set(settings.get('add_data', False))
                    self.optimize.set(settings.get('optimize', True))
                    self.upx_compress.set(settings.get('upx_compress', False))

                    # تحديث معاينة الأمر
                    self.update_command_preview()
        except Exception as e:
            print(f"خطأ في تحميل الإعدادات: {e}")

    def run(self):
        """تشغيل التطبيق"""
        # تحديث معاينة الأمر الأولية
        self.update_command_preview()

        # ربط أحداث الإغلاق
        self.root.protocol("WM_DELETE_WINDOW", self.on_closing)

        # تشغيل الحلقة الرئيسية
        self.root.mainloop()

    def on_closing(self):
        """عند إغلاق التطبيق"""
        if self.is_converting:
            if messagebox.askokcancel("إغلاق", "عملية تحويل جارية. هل تريد الإغلاق؟"):
                self.save_settings()
                self.root.destroy()
        else:
            self.save_settings()
            self.root.destroy()

# تشغيل التطبيق
if __name__ == "__main__":
    app = PyInstallerGUI()
    app.run()
