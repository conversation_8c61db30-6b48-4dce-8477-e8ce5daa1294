import os
import subprocess
import sys
import tkinter as tk
from tkinter import filedialog, messagebox, ttk
import traceback
import threading
import json
import logging
from datetime import datetime
import shutil
from pathlib import Path

# إعداد نظام السجلات
logging.basicConfig(
    filename='conversion_log.txt',
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    encoding='utf-8'
)

# وظيفة لتثبيت المكتبات تلقائياً إذا لم تكن مثبتة
def install_package(package):
    try:
        __import__(package)
    except ImportError:
        print(f"Installing {package}...")
        subprocess.check_call([sys.executable, "-m", "pip", "install", package])

# تثبيت المكتبات المطلوبة
required_packages = ["PyInstaller", "Pillow"]
for package in required_packages:
    install_package(package)

# الآن استيرادها
import PyInstaller.__main__
try:
    from PIL import Image, ImageTk
    PIL_AVAILABLE = True
except ImportError:
    PIL_AVAILABLE = False

# ألوان وتصميم الواجهة الزجاجية المحسنة
COLORS = {
    'bg_primary': '#0a0a0f',           # خلفية رئيسية داكنة
    'bg_secondary': '#1a1a2e',        # خلفية ثانوية
    'bg_glass': '#2d2d44',            # خلفية زجاجية
    'bg_card': '#3a3a5c',             # خلفية البطاقات
    'accent': '#6c5ce7',              # لون أساسي بنفسجي
    'accent_hover': '#a29bfe',        # لون التمرير
    'accent_light': '#74b9ff',        # لون فاتح
    'text_primary': '#ffffff',        # نص أساسي
    'text_secondary': '#b2bec3',      # نص ثانوي
    'text_muted': '#636e72',          # نص خافت
    'success': '#00b894',             # لون النجاح
    'warning': '#fdcb6e',             # لون التحذير
    'error': '#e17055',               # لون الخطأ
    'glass_border': '#6c5ce7',        # حدود زجاجية
    'shadow': '#00000040',            # ظل
    'gradient_start': '#6c5ce7',      # بداية التدرج
    'gradient_end': '#74b9ff'         # نهاية التدرج
}

class GlassFrame(tk.Frame):
    """إطار زجاجي مخصص محسن"""
    def __init__(self, parent, style='default', **kwargs):
        super().__init__(parent, **kwargs)

        if style == 'card':
            # تصميم بطاقة مرتفعة
            self.configure(
                bg=COLORS['bg_card'],
                relief='flat',
                bd=0,
                highlightbackground=COLORS['glass_border'],
                highlightthickness=2,
                padx=15,
                pady=15
            )
        elif style == 'header':
            # تصميم رأس الصفحة
            self.configure(
                bg=COLORS['bg_glass'],
                relief='flat',
                bd=0,
                highlightbackground=COLORS['gradient_start'],
                highlightthickness=3,
                padx=20,
                pady=20
            )
        else:
            # التصميم الافتراضي
            self.configure(
                bg=COLORS['bg_glass'],
                relief='flat',
                bd=0,
                highlightbackground=COLORS['glass_border'],
                highlightthickness=1,
                padx=10,
                pady=10
            )

        # إضافة تأثير الظل
        self.bind('<Enter>', self._on_enter)
        self.bind('<Leave>', self._on_leave)

    def _on_enter(self, event):
        """تأثير عند التمرير"""
        self.configure(highlightthickness=2)

    def _on_leave(self, event):
        """إزالة التأثير عند المغادرة"""
        self.configure(highlightthickness=1)

class AnimatedButton(tk.Button):
    """زر متحرك مخصص محسن"""
    def __init__(self, parent, style='primary', size='medium', **kwargs):
        super().__init__(parent, **kwargs)

        # تحديد الأحجام
        sizes = {
            'small': {'font': ('Segoe UI', 9, 'bold'), 'padx': 15, 'pady': 8},
            'medium': {'font': ('Segoe UI', 10, 'bold'), 'padx': 20, 'pady': 10},
            'large': {'font': ('Segoe UI', 12, 'bold'), 'padx': 25, 'pady': 12}
        }

        # تحديد الأنماط
        styles = {
            'primary': {
                'bg': COLORS['accent'],
                'hover_bg': COLORS['accent_hover'],
                'fg': COLORS['text_primary']
            },
            'success': {
                'bg': COLORS['success'],
                'hover_bg': '#00a085',
                'fg': COLORS['text_primary']
            },
            'warning': {
                'bg': COLORS['warning'],
                'hover_bg': '#e6b800',
                'fg': COLORS['text_primary']
            },
            'secondary': {
                'bg': COLORS['bg_card'],
                'hover_bg': COLORS['bg_glass'],
                'fg': COLORS['text_primary']
            }
        }

        self.style_config = styles.get(style, styles['primary'])
        size_config = sizes.get(size, sizes['medium'])

        self.configure(
            bg=self.style_config['bg'],
            fg=self.style_config['fg'],
            relief='flat',
            bd=0,
            font=size_config['font'],
            cursor='hand2',
            activebackground=self.style_config['hover_bg'],
            activeforeground=self.style_config['fg'],
            padx=size_config['padx'],
            pady=size_config['pady'],
            borderwidth=0,
            highlightthickness=0
        )

        # إضافة تأثيرات التفاعل
        self.bind('<Enter>', self._on_enter)
        self.bind('<Leave>', self._on_leave)
        self.bind('<Button-1>', self._on_click)
        self.bind('<ButtonRelease-1>', self._on_release)

    def _on_enter(self, event):
        """تأثير عند التمرير"""
        self.configure(bg=self.style_config['hover_bg'])
        # تأثير رفع الزر
        self.configure(relief='raised', bd=1)

    def _on_leave(self, event):
        """إزالة التأثير عند المغادرة"""
        self.configure(bg=self.style_config['bg'])
        self.configure(relief='flat', bd=0)

    def _on_click(self, event):
        """تأثير عند الضغط"""
        self.configure(relief='sunken', bd=1)

    def _on_release(self, event):
        """تأثير عند الإفلات"""
        self.configure(relief='raised', bd=1)

class PyInstallerGUI:
    def __init__(self):
        self.root = tk.Tk()
        self.setup_window()
        self.setup_variables()
        self.create_widgets()
        self.load_settings()

    def setup_window(self):
        """إعداد النافذة الرئيسية المحسنة - تخطيط مدمج"""
        self.root.title("🚀 Python to EXE Converter Pro v2.0")
        self.root.geometry("1400x900")
        self.root.minsize(1200, 800)
        self.root.state('zoomed')  # ملء الشاشة

        # تطبيق تدرج لوني للخلفية
        self.root.configure(bg=COLORS['bg_primary'])

        # جعل النافذة في المنتصف
        self.center_window()

        # إعداد الأيقونة إذا كانت متوفرة
        try:
            self.root.iconbitmap('icon.ico')
        except:
            pass

        # إضافة تأثيرات النافذة
        self.root.attributes('-alpha', 0.98)  # شفافية خفيفة

        # تحسين الخطوط
        self.setup_fonts()

    def setup_fonts(self):
        """إعداد الخطوط المخصصة"""
        try:
            # تحديد الخطوط المتاحة
            import tkinter.font as tkFont

            # خط العنوان الرئيسي
            self.title_font = tkFont.Font(
                family='Segoe UI',
                size=28,
                weight='bold'
            )

            # خط العناوين الفرعية
            self.subtitle_font = tkFont.Font(
                family='Segoe UI',
                size=14,
                weight='normal'
            )

            # خط الأزرار
            self.button_font = tkFont.Font(
                family='Segoe UI',
                size=11,
                weight='bold'
            )

            # خط النصوص العادية
            self.normal_font = tkFont.Font(
                family='Segoe UI',
                size=10,
                weight='normal'
            )

        except Exception:
            # استخدام الخطوط الافتراضية في حالة الفشل
            self.title_font = ('Segoe UI', 28, 'bold')
            self.subtitle_font = ('Segoe UI', 14)
            self.button_font = ('Segoe UI', 11, 'bold')
            self.normal_font = ('Segoe UI', 10)

    def center_window(self):
        """وضع النافذة في منتصف الشاشة"""
        self.root.update_idletasks()
        width = self.root.winfo_width()
        height = self.root.winfo_height()
        x = (self.root.winfo_screenwidth() // 2) - (width // 2)
        y = (self.root.winfo_screenheight() // 2) - (height // 2)
        self.root.geometry(f'{width}x{height}+{x}+{y}')

    def setup_variables(self):
        """إعداد المتغيرات"""
        self.selected_path = tk.StringVar()
        self.output_folder_var = tk.StringVar()
        self.icon_path = tk.StringVar()

        # خيارات التحويل
        self.one_file = tk.BooleanVar(value=True)
        self.hide_console = tk.BooleanVar(value=True)
        self.debug_mode = tk.BooleanVar()
        self.add_data = tk.BooleanVar()
        self.optimize = tk.BooleanVar(value=True)
        self.upx_compress = tk.BooleanVar()

        # حالة التطبيق
        self.is_converting = False

    def create_widgets(self):
        """إنشاء عناصر الواجهة - تخطيط مدمج بدون تمرير"""
        # العنوان الرئيسي المدمج
        self.create_compact_header()

        # الإطار الرئيسي - تخطيط أفقي
        main_container = tk.Frame(self.root, bg=COLORS['bg_primary'])
        main_container.pack(fill='both', expand=True, padx=10, pady=(0, 10))

        # العمود الأيمن - الإعدادات والخيارات (عرض ثابت) - للغة العربية
        right_panel = GlassFrame(main_container, style='card')
        right_panel.pack(side='right', fill='y', padx=(5, 0))
        right_panel.configure(width=480)  # عرض ثابت للعمود الأيمن
        right_panel.pack_propagate(False)  # منع تغيير الحجم

        # العمود الأيسر - المعاينة والسجل (يملأ باقي المساحة) - للغة العربية
        left_panel = GlassFrame(main_container, style='card')
        left_panel.pack(side='left', fill='both', expand=True, padx=(0, 5))

        # ملء العمود الأيمن (الإعدادات)
        self.create_settings_panel_content(right_panel)

        # ملء العمود الأيسر (المعاينة والسجل)
        self.create_preview_panel_content(left_panel)

        # شريط الحالة
        self.create_status_bar()

    def create_compact_header(self):
        """إنشاء عنوان مدمج مضغوط"""
        header_frame = GlassFrame(self.root, style='header')
        header_frame.pack(fill='x', padx=10, pady=(10, 5))

        # إطار العنوان الأفقي
        title_container = tk.Frame(header_frame, bg=COLORS['bg_glass'])
        title_container.pack(fill='x', padx=15, pady=10)

        # معلومات الإصدار على اليسار (للغة العربية)
        version_frame = tk.Frame(title_container, bg=COLORS['bg_glass'])
        version_frame.pack(side='left')

        tk.Label(
            version_frame,
            text="v2.0",
            font=('Segoe UI', 12, 'bold'),
            fg=COLORS['accent_light'],
            bg=COLORS['bg_glass']
        ).pack()

        # الأيقونة والعنوان في المنتصف
        icon_title_frame = tk.Frame(title_container, bg=COLORS['bg_glass'])
        icon_title_frame.pack(expand=True)

        title_text_frame = tk.Frame(icon_title_frame, bg=COLORS['bg_glass'])
        title_text_frame.pack(side='right', padx=(10, 0))

        tk.Label(
            title_text_frame,
            text="محول ملفات Python إلى EXE",
            font=('Segoe UI', 18, 'bold'),
            fg=COLORS['text_primary'],
            bg=COLORS['bg_glass']
        ).pack(anchor='e')

        tk.Label(
            title_text_frame,
            text="تحويل ملفات Python إلى تطبيقات تنفيذية بواجهة احترافية",
            font=('Segoe UI', 10),
            fg=COLORS['text_secondary'],
            bg=COLORS['bg_glass']
        ).pack(anchor='e')

        # الأيقونة على اليمين
        tk.Label(
            icon_title_frame,
            text="🚀",
            font=('Segoe UI', 24),
            fg=COLORS['accent'],
            bg=COLORS['bg_glass']
        ).pack(side='right')

    def create_settings_panel_content(self, parent):
        """محتوى العمود الأيمن - الإعدادات (للغة العربية)"""
        # قسم اختيار الملفات
        self.create_file_selection_compact(parent)

        # قسم الخيارات المتقدمة
        self.create_advanced_options_compact(parent)

        # قسم التحويل
        self.create_conversion_controls(parent)

    def create_preview_panel_content(self, parent):
        """محتوى العمود الأيسر - المعاينة والسجل (للغة العربية)"""
        # قسم معاينة الأمر
        self.create_command_preview_compact(parent)

        # قسم السجل والملفات المحولة
        self.create_log_section_compact(parent)

    def create_header(self):
        """إنشاء العنوان الرئيسي المحسن"""
        # إطار العنوان مع تصميم خاص
        header_frame = GlassFrame(self.scrollable_frame, style='header')
        header_frame.pack(fill='x', padx=15, pady=(15, 20))

        # إطار داخلي للتنسيق
        inner_frame = tk.Frame(header_frame, bg=COLORS['bg_glass'])
        inner_frame.pack(fill='x', padx=20, pady=15)

        # العنوان الرئيسي مع تأثير متدرج
        title_frame = tk.Frame(inner_frame, bg=COLORS['bg_glass'])
        title_frame.pack(pady=(10, 5))

        # أيقونة العنوان
        icon_label = tk.Label(
            title_frame,
            text="🚀",
            font=('Segoe UI', 32),
            fg=COLORS['accent'],
            bg=COLORS['bg_glass']
        )
        icon_label.pack(side='left', padx=(0, 15))

        # نص العنوان
        title_text_frame = tk.Frame(title_frame, bg=COLORS['bg_glass'])
        title_text_frame.pack(side='left', fill='x', expand=True)

        title_label = tk.Label(
            title_text_frame,
            text="Python to EXE Converter",
            font=self.title_font,
            fg=COLORS['text_primary'],
            bg=COLORS['bg_glass']
        )
        title_label.pack(anchor='w')

        version_label = tk.Label(
            title_text_frame,
            text="Professional v2.0",
            font=('Segoe UI', 12, 'italic'),
            fg=COLORS['accent_light'],
            bg=COLORS['bg_glass']
        )
        version_label.pack(anchor='w')

        # خط فاصل مع تدرج
        separator_frame = tk.Frame(inner_frame, height=3, bg=COLORS['accent'])
        separator_frame.pack(fill='x', pady=(15, 10))

        # العنوان الفرعي
        subtitle_label = tk.Label(
            inner_frame,
            text="✨ تحويل ملفات Python إلى تطبيقات تنفيذية بواجهة احترافية وتصميم زجاجي متقدم",
            font=self.subtitle_font,
            fg=COLORS['text_secondary'],
            bg=COLORS['bg_glass'],
            wraplength=800,
            justify='center'
        )
        subtitle_label.pack(pady=(0, 10))

        # معلومات إضافية
        info_frame = tk.Frame(inner_frame, bg=COLORS['bg_glass'])
        info_frame.pack(fill='x', pady=(5, 10))

        features = [
            "🎨 تصميم زجاجي احترافي",
            "⚡ تحويل سريع وموثوق",
            "🔧 خيارات متقدمة",
            "📊 مراقبة مباشرة"
        ]

        for i, feature in enumerate(features):
            feature_label = tk.Label(
                info_frame,
                text=feature,
                font=('Segoe UI', 9),
                fg=COLORS['text_muted'],
                bg=COLORS['bg_glass']
            )
            if i < 2:
                feature_label.pack(side='left', padx=(0, 30))
            else:
                feature_label.pack(side='right', padx=(30, 0))

    def create_file_selection_section(self):
        """قسم اختيار الملفات المحسن"""
        file_frame = GlassFrame(self.scrollable_frame, style='card')
        file_frame.pack(fill='x', padx=15, pady=10)

        # عنوان القسم مع أيقونة
        header_frame = tk.Frame(file_frame, bg=COLORS['bg_card'])
        header_frame.pack(fill='x', pady=(0, 15))

        section_icon = tk.Label(
            header_frame,
            text="📁",
            font=('Segoe UI', 20),
            fg=COLORS['accent'],
            bg=COLORS['bg_card']
        )
        section_icon.pack(side='left', padx=(0, 10))

        section_title = tk.Label(
            header_frame,
            text="اختيار الملفات",
            font=('Segoe UI', 18, 'bold'),
            fg=COLORS['text_primary'],
            bg=COLORS['bg_card']
        )
        section_title.pack(side='left')

        # خط فاصل
        separator = tk.Frame(file_frame, height=2, bg=COLORS['accent'])
        separator.pack(fill='x', pady=(0, 20))

        # إطار اختيار الملف المصدر
        source_container = GlassFrame(file_frame)
        source_container.pack(fill='x', pady=(0, 15))

        source_label_frame = tk.Frame(source_container, bg=COLORS['bg_glass'])
        source_label_frame.pack(fill='x', pady=(0, 8))

        tk.Label(
            source_label_frame,
            text="🎯 الملف المصدر:",
            font=self.normal_font,
            fg=COLORS['text_primary'],
            bg=COLORS['bg_glass']
        ).pack(side='left')

        # مؤشر الحالة
        self.source_status = tk.Label(
            source_label_frame,
            text="لم يتم اختيار ملف",
            font=('Segoe UI', 9, 'italic'),
            fg=COLORS['warning'],
            bg=COLORS['bg_glass']
        )
        self.source_status.pack(side='right')

        source_entry_frame = tk.Frame(source_container, bg=COLORS['bg_glass'])
        source_entry_frame.pack(fill='x', pady=(0, 10))

        # تحسين تصميم حقل الإدخال
        self.source_entry = tk.Entry(
            source_entry_frame,
            textvariable=self.selected_path,
            font=self.normal_font,
            bg=COLORS['bg_secondary'],
            fg=COLORS['text_primary'],
            relief='flat',
            bd=0,
            insertbackground=COLORS['accent'],
            selectbackground=COLORS['accent'],
            selectforeground=COLORS['text_primary']
        )
        self.source_entry.pack(side='left', fill='x', expand=True, padx=(0, 10), ipady=8)

        # إضافة تأثير التركيز
        self.source_entry.bind('<FocusIn>', lambda e: self.source_entry.configure(bg=COLORS['bg_card']))
        self.source_entry.bind('<FocusOut>', lambda e: self.source_entry.configure(bg=COLORS['bg_secondary']))

        # أزرار التصفح المحسنة
        button_frame = tk.Frame(source_entry_frame, bg=COLORS['bg_glass'])
        button_frame.pack(side='right')

        AnimatedButton(
            button_frame,
            text="📄 ملف",
            command=self.browse_file,
            style='primary',
            size='small'
        ).pack(side='left', padx=(0, 5))

        AnimatedButton(
            button_frame,
            text="📁 مجلد",
            command=self.browse_folder,
            style='secondary',
            size='small'
        ).pack(side='left')

        # إطار مجلد الحفظ
        output_container = GlassFrame(file_frame)
        output_container.pack(fill='x')

        output_label_frame = tk.Frame(output_container, bg=COLORS['bg_glass'])
        output_label_frame.pack(fill='x', pady=(0, 8))

        tk.Label(
            output_label_frame,
            text="💾 مجلد الحفظ:",
            font=self.normal_font,
            fg=COLORS['text_primary'],
            bg=COLORS['bg_glass']
        ).pack(side='left')

        # مؤشر حالة مجلد الحفظ
        self.output_status = tk.Label(
            output_label_frame,
            text="سيتم السؤال عند التحويل",
            font=('Segoe UI', 9, 'italic'),
            fg=COLORS['text_muted'],
            bg=COLORS['bg_glass']
        )
        self.output_status.pack(side='right')

        output_entry_frame = tk.Frame(output_container, bg=COLORS['bg_glass'])
        output_entry_frame.pack(fill='x', pady=(0, 10))

        self.output_entry = tk.Entry(
            output_entry_frame,
            textvariable=self.output_folder_var,
            font=self.normal_font,
            bg=COLORS['bg_secondary'],
            fg=COLORS['text_primary'],
            relief='flat',
            bd=0,
            insertbackground=COLORS['accent'],
            selectbackground=COLORS['accent'],
            selectforeground=COLORS['text_primary']
        )
        self.output_entry.pack(side='left', fill='x', expand=True, padx=(0, 10), ipady=8)

        # إضافة تأثير التركيز
        self.output_entry.bind('<FocusIn>', lambda e: self.output_entry.configure(bg=COLORS['bg_card']))
        self.output_entry.bind('<FocusOut>', lambda e: self.output_entry.configure(bg=COLORS['bg_secondary']))

        AnimatedButton(
            output_entry_frame,
            text="📁 تصفح",
            command=self.browse_output_folder,
            style='primary',
            size='small'
        ).pack(side='right')

    def create_file_selection_compact(self, parent):
        """قسم اختيار الملفات المدمج"""
        # عنوان القسم
        title_frame = tk.Frame(parent, bg=COLORS['bg_card'])
        title_frame.pack(fill='x', pady=(0, 10))

        tk.Label(
            title_frame,
            text="📁 اختيار الملفات",
            font=('Segoe UI', 14, 'bold'),
            fg=COLORS['text_primary'],
            bg=COLORS['bg_card']
        ).pack(side='right')  # تغيير إلى اليمين للغة العربية

        # خط فاصل
        separator = tk.Frame(parent, height=1, bg=COLORS['accent'])
        separator.pack(fill='x', pady=(0, 15))

        # الملف المصدر
        source_frame = tk.Frame(parent, bg=COLORS['bg_card'])
        source_frame.pack(fill='x', pady=(0, 10))

        tk.Label(
            source_frame,
            text="🎯 الملف المصدر:",
            font=('Segoe UI', 10, 'bold'),
            fg=COLORS['text_primary'],
            bg=COLORS['bg_card']
        ).pack(anchor='e', pady=(0, 5))  # تغيير إلى اليمين للغة العربية

        source_input_frame = tk.Frame(source_frame, bg=COLORS['bg_card'])
        source_input_frame.pack(fill='x')

        self.source_entry_compact = tk.Entry(
            source_input_frame,
            textvariable=self.selected_path,
            font=('Segoe UI', 9),
            bg=COLORS['bg_secondary'],
            fg=COLORS['text_primary'],
            relief='flat',
            bd=0,
            insertbackground=COLORS['accent']
        )
        self.source_entry_compact.pack(side='left', fill='x', expand=True, padx=(0, 5), ipady=6)

        # أزرار التصفح (ترتيب عربي)
        btn_frame = tk.Frame(source_input_frame, bg=COLORS['bg_card'])
        btn_frame.pack(side='right')

        AnimatedButton(
            btn_frame,
            text="📁 مجلد",
            command=self.browse_folder,
            style='secondary',
            size='small'
        ).pack(side='right', padx=(2, 0))

        AnimatedButton(
            btn_frame,
            text="📄 ملف",
            command=self.browse_file,
            style='primary',
            size='small'
        ).pack(side='right')

        # مجلد الحفظ
        output_frame = tk.Frame(parent, bg=COLORS['bg_card'])
        output_frame.pack(fill='x', pady=(10, 0))

        tk.Label(
            output_frame,
            text="💾 مجلد الحفظ:",
            font=('Segoe UI', 10, 'bold'),
            fg=COLORS['text_primary'],
            bg=COLORS['bg_card']
        ).pack(anchor='e', pady=(0, 5))  # تغيير إلى اليمين للغة العربية

        output_input_frame = tk.Frame(output_frame, bg=COLORS['bg_card'])
        output_input_frame.pack(fill='x')

        self.output_entry_compact = tk.Entry(
            output_input_frame,
            textvariable=self.output_folder_var,
            font=('Segoe UI', 9),
            bg=COLORS['bg_secondary'],
            fg=COLORS['text_primary'],
            relief='flat',
            bd=0,
            insertbackground=COLORS['accent']
        )
        self.output_entry_compact.pack(side='left', fill='x', expand=True, padx=(0, 5), ipady=6)

        AnimatedButton(
            output_input_frame,
            text="📁",
            command=self.browse_output_folder,
            style='primary',
            size='small'
        ).pack(side='right')

    def create_advanced_options_compact(self, parent):
        """قسم الخيارات المتقدمة المدمج"""
        # فاصل
        tk.Frame(parent, height=20, bg=COLORS['bg_card']).pack(fill='x')

        # عنوان القسم
        title_frame = tk.Frame(parent, bg=COLORS['bg_card'])
        title_frame.pack(fill='x', pady=(0, 10))

        tk.Label(
            title_frame,
            text="⚙️ الخيارات المتقدمة",
            font=('Segoe UI', 14, 'bold'),
            fg=COLORS['text_primary'],
            bg=COLORS['bg_card']
        ).pack(side='right')  # تغيير إلى اليمين للغة العربية

        # خط فاصل
        separator = tk.Frame(parent, height=1, bg=COLORS['accent'])
        separator.pack(fill='x', pady=(0, 15))

        # الخيارات في شبكة مدمجة
        options_grid = tk.Frame(parent, bg=COLORS['bg_card'])
        options_grid.pack(fill='x')

        # الصف الأول (ترتيب عربي: من اليمين إلى اليسار)
        row1 = tk.Frame(options_grid, bg=COLORS['bg_card'])
        row1.pack(fill='x', pady=(0, 8))

        self.create_compact_checkbox(row1, "🖥️ إخفاء الكونسول", self.hide_console).pack(side='right', anchor='e')
        self.create_compact_checkbox(row1, "📦 ملف واحد", self.one_file).pack(side='left', anchor='w')

        # الصف الثاني (ترتيب عربي)
        row2 = tk.Frame(options_grid, bg=COLORS['bg_card'])
        row2.pack(fill='x', pady=(0, 8))

        self.create_compact_checkbox(row2, "⚡ تحسين الحجم", self.optimize).pack(side='right', anchor='e')
        self.create_compact_checkbox(row2, "🐛 وضع التصحيح", self.debug_mode).pack(side='left', anchor='w')

        # الصف الثالث (ترتيب عربي)
        row3 = tk.Frame(options_grid, bg=COLORS['bg_card'])
        row3.pack(fill='x', pady=(0, 15))

        self.create_compact_checkbox(row3, "🗜️ ضغط UPX", self.upx_compress).pack(side='right', anchor='e')
        self.create_compact_checkbox(row3, "📁 إضافة ملفات", self.add_data).pack(side='left', anchor='w')

        # أيقونة التطبيق
        icon_frame = tk.Frame(parent, bg=COLORS['bg_card'])
        icon_frame.pack(fill='x', pady=(0, 10))

        tk.Label(
            icon_frame,
            text="🎨 أيقونة التطبيق:",
            font=('Segoe UI', 10, 'bold'),
            fg=COLORS['text_primary'],
            bg=COLORS['bg_card']
        ).pack(anchor='w', pady=(0, 5))

        icon_input_frame = tk.Frame(icon_frame, bg=COLORS['bg_card'])
        icon_input_frame.pack(fill='x')

        self.icon_entry_compact = tk.Entry(
            icon_input_frame,
            textvariable=self.icon_path,
            font=('Segoe UI', 9),
            bg=COLORS['bg_secondary'],
            fg=COLORS['text_primary'],
            relief='flat',
            bd=0,
            insertbackground=COLORS['accent']
        )
        self.icon_entry_compact.pack(side='left', fill='x', expand=True, padx=(0, 5), ipady=6)

        AnimatedButton(
            icon_input_frame,
            text="🎨",
            command=self.browse_icon,
            style='primary',
            size='small'
        ).pack(side='right')

    def create_compact_checkbox(self, parent, text, variable):
        """إنشاء checkbox مدمج"""
        frame = tk.Frame(parent, bg=COLORS['bg_card'])

        checkbox = tk.Checkbutton(
            frame,
            text=text,
            variable=variable,
            font=('Segoe UI', 9),
            fg=COLORS['text_primary'],
            bg=COLORS['bg_card'],
            selectcolor=COLORS['bg_secondary'],
            activebackground=COLORS['bg_card'],
            activeforeground=COLORS['text_primary'],
            command=self.update_command_preview
        )
        checkbox.pack()

        return frame

    def create_conversion_controls(self, parent):
        """أزرار التحكم في التحويل"""
        # فاصل
        tk.Frame(parent, height=20, bg=COLORS['bg_card']).pack(fill='x')

        # عنوان القسم
        title_frame = tk.Frame(parent, bg=COLORS['bg_card'])
        title_frame.pack(fill='x', pady=(0, 10))

        tk.Label(
            title_frame,
            text="🚀 التحويل",
            font=('Segoe UI', 14, 'bold'),
            fg=COLORS['text_primary'],
            bg=COLORS['bg_card']
        ).pack(side='right')  # تغيير إلى اليمين للغة العربية

        # خط فاصل
        separator = tk.Frame(parent, height=1, bg=COLORS['accent'])
        separator.pack(fill='x', pady=(0, 15))

        # أزرار التحكم
        buttons_frame = tk.Frame(parent, bg=COLORS['bg_card'])
        buttons_frame.pack(fill='x', pady=(0, 15))

        self.convert_btn = AnimatedButton(
            buttons_frame,
            text="🚀 بدء التحويل",
            command=self.start_conversion,
            style='success',
            size='medium'
        )
        self.convert_btn.pack(fill='x', pady=(0, 8))

        # أزرار مساعدة
        help_frame = tk.Frame(buttons_frame, bg=COLORS['bg_card'])
        help_frame.pack(fill='x')

        AnimatedButton(
            help_frame,
            text="👁️ معاينة",
            command=self.show_command_preview_inline,
            style='secondary',
            size='small'
        ).pack(side='right', fill='x', expand=True, padx=(5, 0))

        AnimatedButton(
            help_frame,
            text="🔍 فحص",
            command=self.check_requirements,
            style='secondary',
            size='small'
        ).pack(side='left', fill='x', expand=True, padx=(0, 5))

        # شريط التقدم
        self.progress_bar = ttk.Progressbar(
            parent,
            mode='indeterminate',
            style='Custom.Horizontal.TProgressbar'
        )
        self.progress_bar.pack(fill='x', pady=(10, 5))

        # تسمية الحالة
        self.status_label = tk.Label(
            parent,
            text="جاهز للتحويل",
            font=('Segoe UI', 10),
            fg=COLORS['success'],
            bg=COLORS['bg_card']
        )
        self.status_label.pack(pady=(0, 10))

    def create_command_preview_compact(self, parent):
        """قسم معاينة الأمر المدمج"""
        # عنوان القسم
        title_frame = tk.Frame(parent, bg=COLORS['bg_card'])
        title_frame.pack(fill='x', pady=(0, 10))

        tk.Label(
            title_frame,
            text="👁️ معاينة الأمر",
            font=('Segoe UI', 14, 'bold'),
            fg=COLORS['text_primary'],
            bg=COLORS['bg_card']
        ).pack(side='right')  # تغيير إلى اليمين للغة العربية

        # خط فاصل
        separator = tk.Frame(parent, height=1, bg=COLORS['accent'])
        separator.pack(fill='x', pady=(0, 15))

        # منطقة النص
        self.command_text = tk.Text(
            parent,
            height=6,
            font=('Consolas', 9),
            bg=COLORS['bg_secondary'],
            fg=COLORS['text_primary'],
            relief='flat',
            bd=0,
            wrap=tk.WORD,
            state='disabled',
            padx=10,
            pady=10
        )
        self.command_text.pack(fill='x', pady=(0, 15))

    def create_log_section_compact(self, parent):
        """قسم السجل والملفات المحولة المدمج"""
        # عنوان القسم
        title_frame = tk.Frame(parent, bg=COLORS['bg_card'])
        title_frame.pack(fill='x', pady=(0, 10))

        tk.Label(
            title_frame,
            text="📋 السجل والملفات المحولة",
            font=('Segoe UI', 14, 'bold'),
            fg=COLORS['text_primary'],
            bg=COLORS['bg_card']
        ).pack(side='right')  # تغيير إلى اليمين للغة العربية

        # خط فاصل
        separator = tk.Frame(parent, height=1, bg=COLORS['accent'])
        separator.pack(fill='x', pady=(0, 15))

        # إطار التبويبات
        notebook = ttk.Notebook(parent)
        notebook.pack(fill='both', expand=True, pady=(0, 10))

        # تبويب الملفات المحولة
        converted_frame = tk.Frame(notebook, bg=COLORS['bg_secondary'])
        notebook.add(converted_frame, text="الملفات المحولة")

        self.converted_listbox = tk.Listbox(
            converted_frame,
            font=('Segoe UI', 9),
            bg=COLORS['bg_secondary'],
            fg=COLORS['text_primary'],
            selectbackground=COLORS['accent'],
            relief='flat',
            bd=0
        )
        self.converted_listbox.pack(fill='both', expand=True, padx=5, pady=5)

        # تبويب السجل
        log_text_frame = tk.Frame(notebook, bg=COLORS['bg_secondary'])
        notebook.add(log_text_frame, text="سجل العمليات")

        # إطار السجل مع شريط تمرير
        log_container = tk.Frame(log_text_frame, bg=COLORS['bg_secondary'])
        log_container.pack(fill='both', expand=True, padx=5, pady=5)

        self.log_text = tk.Text(
            log_container,
            font=('Consolas', 8),
            bg=COLORS['bg_secondary'],
            fg=COLORS['text_primary'],
            relief='flat',
            bd=0,
            wrap=tk.WORD,
            state='disabled'
        )
        self.log_text.pack(side='left', fill='both', expand=True)

        # شريط تمرير للسجل
        log_scrollbar = ttk.Scrollbar(log_container, orient="vertical", command=self.log_text.yview)
        self.log_text.configure(yscrollcommand=log_scrollbar.set)
        log_scrollbar.pack(side="right", fill="y")

    def show_command_preview_inline(self):
        """عرض معاينة الأمر في المنطقة المخصصة"""
        self.update_command_preview()

    def browse_file(self):
        """تصفح ملف Python مع تحديث الحالة"""
        file_path = filedialog.askopenfilename(
            title="اختر ملف Python",
            filetypes=[("Python Files", "*.py"), ("All Files", "*.*")]
        )
        if file_path:
            self.selected_path.set(file_path)
            # تحديث مؤشر الحالة
            filename = os.path.basename(file_path)
            self.source_status.configure(
                text=f"✅ {filename}",
                fg=COLORS['success']
            )
            self.update_command_preview()

    def browse_folder(self):
        """تصفح مجلد مع تحديث الحالة"""
        folder_path = filedialog.askdirectory(title="اختر مجلد Python")
        if folder_path:
            self.selected_path.set(folder_path)
            # عد ملفات Python في المجلد
            try:
                py_files = [f for f in os.listdir(folder_path) if f.endswith('.py')]
                count = len(py_files)
                if count > 0:
                    self.source_status.configure(
                        text=f"✅ {count} ملف Python",
                        fg=COLORS['success']
                    )
                else:
                    self.source_status.configure(
                        text="⚠️ لا توجد ملفات Python",
                        fg=COLORS['warning']
                    )
            except Exception:
                self.source_status.configure(
                    text="✅ مجلد محدد",
                    fg=COLORS['success']
                )
            self.update_command_preview()

    def browse_output_folder(self):
        """تصفح مجلد الحفظ مع تحديث الحالة"""
        folder_path = filedialog.askdirectory(title="اختر مجلد الحفظ")
        if folder_path:
            self.output_folder_var.set(folder_path)
            # تحديث مؤشر الحالة
            folder_name = os.path.basename(folder_path)
            self.output_status.configure(
                text=f"✅ {folder_name}",
                fg=COLORS['success']
            )
            self.update_command_preview()

    def create_advanced_options_section(self):
        """قسم الخيارات المتقدمة"""
        options_frame = GlassFrame(self.scrollable_frame)
        options_frame.pack(fill='x', padx=20, pady=10)

        # عنوان القسم
        section_title = tk.Label(
            options_frame,
            text="⚙️ الخيارات المتقدمة",
            font=('Segoe UI', 16, 'bold'),
            fg=COLORS['text_primary'],
            bg=COLORS['bg_glass']
        )
        section_title.pack(pady=(15, 10))

        # إطار الخيارات الأساسية
        basic_options_frame = tk.Frame(options_frame, bg=COLORS['bg_glass'])
        basic_options_frame.pack(fill='x', padx=20, pady=10)

        # الصف الأول من الخيارات
        row1 = tk.Frame(basic_options_frame, bg=COLORS['bg_glass'])
        row1.pack(fill='x', pady=5)

        self.create_checkbox(row1, "📦 ملف واحد", self.one_file, "إنشاء ملف exe واحد").pack(side='left', padx=(0, 20))
        self.create_checkbox(row1, "🖥️ إخفاء الكونسول", self.hide_console, "إخفاء نافذة سطر الأوامر").pack(side='left', padx=(0, 20))

        # الصف الثاني من الخيارات
        row2 = tk.Frame(basic_options_frame, bg=COLORS['bg_glass'])
        row2.pack(fill='x', pady=5)

        self.create_checkbox(row2, "🐛 وضع التصحيح", self.debug_mode, "تفعيل وضع التصحيح").pack(side='left', padx=(0, 20))
        self.create_checkbox(row2, "⚡ تحسين الحجم", self.optimize, "تحسين حجم الملف").pack(side='left', padx=(0, 20))

        # الصف الثالث من الخيارات
        row3 = tk.Frame(basic_options_frame, bg=COLORS['bg_glass'])
        row3.pack(fill='x', pady=5)

        self.create_checkbox(row3, "📁 إضافة ملفات", self.add_data, "إضافة ملفات إضافية").pack(side='left', padx=(0, 20))
        self.create_checkbox(row3, "🗜️ ضغط UPX", self.upx_compress, "ضغط الملف باستخدام UPX").pack(side='left', padx=(0, 20))

        # إطار الأيقونة
        icon_frame = tk.Frame(options_frame, bg=COLORS['bg_glass'])
        icon_frame.pack(fill='x', padx=20, pady=10)

        tk.Label(
            icon_frame,
            text="🎨 أيقونة التطبيق:",
            font=('Segoe UI', 11, 'bold'),
            fg=COLORS['text_primary'],
            bg=COLORS['bg_glass']
        ).pack(anchor='w')

        icon_entry_frame = tk.Frame(icon_frame, bg=COLORS['bg_glass'])
        icon_entry_frame.pack(fill='x', pady=(5, 15))

        self.icon_entry = tk.Entry(
            icon_entry_frame,
            textvariable=self.icon_path,
            font=('Segoe UI', 10),
            bg=COLORS['bg_secondary'],
            fg=COLORS['text_primary'],
            relief='flat',
            bd=5
        )
        self.icon_entry.pack(side='left', fill='x', expand=True, padx=(0, 10))

        AnimatedButton(
            icon_entry_frame,
            text="🎨 تصفح",
            command=self.browse_icon,
            width=10
        ).pack(side='right')

    def create_checkbox(self, parent, text, variable, tooltip=""):
        """إنشاء checkbox مخصص"""
        frame = tk.Frame(parent, bg=COLORS['bg_glass'])

        checkbox = tk.Checkbutton(
            frame,
            text=text,
            variable=variable,
            font=('Segoe UI', 10),
            fg=COLORS['text_primary'],
            bg=COLORS['bg_glass'],
            selectcolor=COLORS['bg_secondary'],
            activebackground=COLORS['bg_glass'],
            activeforeground=COLORS['text_primary'],
            command=self.update_command_preview
        )
        checkbox.pack()

        # إضافة tooltip إذا كان متوفراً
        if tooltip:
            self.create_tooltip(checkbox, tooltip)

        return frame

    def create_tooltip(self, widget, text):
        """إنشاء tooltip للعنصر"""
        def on_enter(event):
            tooltip = tk.Toplevel()
            tooltip.wm_overrideredirect(True)
            tooltip.wm_geometry(f"+{event.x_root+10}+{event.y_root+10}")

            label = tk.Label(
                tooltip,
                text=text,
                background=COLORS['bg_secondary'],
                foreground=COLORS['text_primary'],
                relief='solid',
                borderwidth=1,
                font=('Segoe UI', 9)
            )
            label.pack()

            widget.tooltip = tooltip

        def on_leave(event):
            if hasattr(widget, 'tooltip'):
                widget.tooltip.destroy()
                del widget.tooltip

        widget.bind('<Enter>', on_enter)
        widget.bind('<Leave>', on_leave)

    def browse_icon(self):
        """تصفح أيقونة"""
        icon_file = filedialog.askopenfilename(
            title="اختر أيقونة",
            filetypes=[
                ("Icon Files", "*.ico"),
                ("PNG Files", "*.png"),
                ("All Files", "*.*")
            ]
        )
        if icon_file:
            self.icon_path.set(icon_file)
            self.update_command_preview()

    def create_command_preview_section(self):
        """قسم معاينة الأمر"""
        preview_frame = GlassFrame(self.scrollable_frame)
        preview_frame.pack(fill='x', padx=20, pady=10)

        # عنوان القسم
        section_title = tk.Label(
            preview_frame,
            text="👁️ معاينة الأمر",
            font=('Segoe UI', 16, 'bold'),
            fg=COLORS['text_primary'],
            bg=COLORS['bg_glass']
        )
        section_title.pack(pady=(15, 10))

        # منطقة النص
        self.command_text = tk.Text(
            preview_frame,
            height=4,
            font=('Consolas', 9),
            bg=COLORS['bg_secondary'],
            fg=COLORS['text_primary'],
            relief='flat',
            bd=5,
            wrap=tk.WORD,
            state='disabled'
        )
        self.command_text.pack(fill='x', padx=20, pady=(0, 15))

    def create_conversion_section(self):
        """قسم التحويل والتقدم"""
        conversion_frame = GlassFrame(self.scrollable_frame)
        conversion_frame.pack(fill='x', padx=20, pady=10)

        # عنوان القسم
        section_title = tk.Label(
            conversion_frame,
            text="🚀 التحويل",
            font=('Segoe UI', 16, 'bold'),
            fg=COLORS['text_primary'],
            bg=COLORS['bg_glass']
        )
        section_title.pack(pady=(15, 10))

        # أزرار التحكم
        buttons_frame = tk.Frame(conversion_frame, bg=COLORS['bg_glass'])
        buttons_frame.pack(pady=10)

        self.convert_btn = AnimatedButton(
            buttons_frame,
            text="🚀 بدء التحويل",
            command=self.start_conversion,
            font=('Segoe UI', 12, 'bold'),
            width=15
        )
        self.convert_btn.pack(side='left', padx=10)

        self.check_btn = AnimatedButton(
            buttons_frame,
            text="🔍 فحص المتطلبات",
            command=self.check_requirements,
            width=15
        )
        self.check_btn.pack(side='left', padx=10)

        self.preview_btn = AnimatedButton(
            buttons_frame,
            text="👁️ معاينة الأمر",
            command=self.show_command_preview,
            width=15
        )
        self.preview_btn.pack(side='left', padx=10)

        # شريط التقدم
        progress_frame = tk.Frame(conversion_frame, bg=COLORS['bg_glass'])
        progress_frame.pack(fill='x', padx=20, pady=10)

        self.progress_bar = ttk.Progressbar(
            progress_frame,
            mode='indeterminate',
            style='Custom.Horizontal.TProgressbar'
        )
        self.progress_bar.pack(fill='x', pady=5)

        # تخصيص شريط التقدم
        style = ttk.Style()
        style.configure(
            'Custom.Horizontal.TProgressbar',
            background=COLORS['accent'],
            troughcolor=COLORS['bg_secondary'],
            borderwidth=0,
            lightcolor=COLORS['accent'],
            darkcolor=COLORS['accent']
        )

        # تسمية الحالة
        self.status_label = tk.Label(
            progress_frame,
            text="جاهز للتحويل",
            font=('Segoe UI', 11),
            fg=COLORS['success'],
            bg=COLORS['bg_glass']
        )
        self.status_label.pack(pady=5)

    def create_log_section(self):
        """قسم السجل والملفات المحولة"""
        log_frame = GlassFrame(self.scrollable_frame)
        log_frame.pack(fill='both', expand=True, padx=20, pady=10)

        # عنوان القسم
        section_title = tk.Label(
            log_frame,
            text="📋 السجل والملفات المحولة",
            font=('Segoe UI', 16, 'bold'),
            fg=COLORS['text_primary'],
            bg=COLORS['bg_glass']
        )
        section_title.pack(pady=(15, 10))

        # إطار التبويبات
        notebook = ttk.Notebook(log_frame)
        notebook.pack(fill='both', expand=True, padx=20, pady=(0, 15))

        # تبويب الملفات المحولة
        converted_frame = tk.Frame(notebook, bg=COLORS['bg_secondary'])
        notebook.add(converted_frame, text="الملفات المحولة")

        self.converted_listbox = tk.Listbox(
            converted_frame,
            font=('Segoe UI', 10),
            bg=COLORS['bg_secondary'],
            fg=COLORS['text_primary'],
            selectbackground=COLORS['accent'],
            relief='flat',
            bd=0
        )
        self.converted_listbox.pack(fill='both', expand=True, padx=10, pady=10)

        # تبويب السجل
        log_text_frame = tk.Frame(notebook, bg=COLORS['bg_secondary'])
        notebook.add(log_text_frame, text="سجل العمليات")

        self.log_text = tk.Text(
            log_text_frame,
            font=('Consolas', 9),
            bg=COLORS['bg_secondary'],
            fg=COLORS['text_primary'],
            relief='flat',
            bd=0,
            wrap=tk.WORD,
            state='disabled'
        )
        self.log_text.pack(fill='both', expand=True, padx=10, pady=10)

        # شريط تمرير للسجل
        log_scrollbar = ttk.Scrollbar(log_text_frame, orient="vertical", command=self.log_text.yview)
        self.log_text.configure(yscrollcommand=log_scrollbar.set)
        log_scrollbar.pack(side="right", fill="y")

    def create_status_bar(self):
        """شريط الحالة"""
        status_frame = tk.Frame(self.root, bg=COLORS['bg_secondary'], height=30)
        status_frame.pack(fill='x', side='bottom')
        status_frame.pack_propagate(False)

        self.status_bar_label = tk.Label(
            status_frame,
            text="جاهز | Python to EXE Converter Pro v2.0",
            font=('Segoe UI', 9),
            fg=COLORS['text_secondary'],
            bg=COLORS['bg_secondary']
        )
        self.status_bar_label.pack(side='left', padx=10, pady=5)
    def update_command_preview(self):
        """تحديث معاينة الأمر"""
        path = self.selected_path.get()
        if not path:
            self.command_text.config(state='normal')
            self.command_text.delete(1.0, tk.END)
            self.command_text.insert(tk.END, "اختر ملف Python أولاً...")
            self.command_text.config(state='disabled')
            return

        cmd_args = ['pyinstaller']

        if self.one_file.get():
            cmd_args.append('--onefile')

        if self.hide_console.get():
            cmd_args.append('--noconsole')

        if self.debug_mode.get():
            cmd_args.append('--debug=all')

        if self.optimize.get():
            cmd_args.append('--optimize=2')

        if self.upx_compress.get():
            cmd_args.append('--upx-dir=upx')

        if self.icon_path.get():
            cmd_args.extend(['--icon', f'"{self.icon_path.get()}"'])

        if self.output_folder_var.get():
            cmd_args.extend(['--distpath', f'"{self.output_folder_var.get()}"'])

        cmd_args.append(f'"{path}"')

        command_text = ' '.join(cmd_args)

        self.command_text.config(state='normal')
        self.command_text.delete(1.0, tk.END)
        self.command_text.insert(tk.END, command_text)
        self.command_text.config(state='disabled')

    def show_command_preview(self):
        """عرض معاينة الأمر في المنطقة المخصصة (بدلاً من نافذة منبثقة)"""
        self.update_command_preview()

    def copy_to_clipboard(self, text):
        """نسخ النص إلى الحافظة"""
        self.root.clipboard_clear()
        self.root.clipboard_append(text)
        self.update_status("تم نسخ الأمر إلى الحافظة")

    def build_command_args(self, path):
        """بناء قائمة أوامر PyInstaller"""
        cmd_args = ['pyinstaller']

        if self.one_file.get():
            cmd_args.append('--onefile')

        if self.hide_console.get():
            cmd_args.append('--noconsole')

        if self.debug_mode.get():
            cmd_args.append('--debug=all')

        if self.optimize.get():
            cmd_args.append('--optimize=2')

        if self.upx_compress.get():
            cmd_args.append('--upx-dir=upx')

        if self.icon_path.get():
            cmd_args.extend(['--icon', self.icon_path.get()])

        if self.output_folder_var.get():
            cmd_args.extend(['--distpath', self.output_folder_var.get()])

        cmd_args.append(path)

        return cmd_args

    def check_requirements(self):
        """فحص المتطلبات"""
        issues = []
        warnings = []

        # تحقق من وجود Python
        if not sys.executable:
            issues.append("❌ لم يتم العثور على Python")
        else:
            warnings.append(f"✅ Python: {sys.version}")

        # تحقق من PyInstaller
        try:
            import PyInstaller
            warnings.append(f"✅ PyInstaller: {PyInstaller.__version__}")
        except ImportError:
            issues.append("❌ PyInstaller غير مثبت")

        # تحقق من مساحة القرص
        try:
            free_space = shutil.disk_usage('.').free / (1024**3)  # GB
            if free_space < 1:
                issues.append(f"⚠️ مساحة القرص أقل من 1 جيجابايت ({free_space:.1f} GB)")
            else:
                warnings.append(f"✅ مساحة القرص: {free_space:.1f} GB")
        except:
            warnings.append("⚠️ لا يمكن فحص مساحة القرص")

        # تحقق من صحة الملف
        path = self.selected_path.get()
        if path and os.path.isfile(path):
            try:
                with open(path, 'r', encoding='utf-8') as f:
                    compile(f.read(), path, 'exec')
                warnings.append("✅ الملف صحيح نحوياً")
            except SyntaxError as e:
                issues.append(f"❌ خطأ في بناء الجملة: {e}")
            except Exception as e:
                warnings.append(f"⚠️ تحذير: {e}")
        elif path:
            warnings.append("ℹ️ سيتم فحص جميع ملفات Python في المجلد")

        # عرض النتائج
        self.show_requirements_result(issues, warnings)

    def show_requirements_result(self, issues, warnings):
        """عرض نتائج فحص المتطلبات في السجل"""
        # مسح السجل وإضافة نتائج الفحص
        self.log_text.config(state='normal')
        self.log_text.delete(1.0, tk.END)

        # إضافة عنوان الفحص
        timestamp = datetime.now().strftime("%H:%M:%S")
        self.log_text.insert(tk.END, f"[{timestamp}] 🔍 نتائج فحص المتطلبات\n")
        self.log_text.insert(tk.END, "=" * 50 + "\n\n")

        # إدراج النتائج
        if issues:
            self.log_text.insert(tk.END, "🚨 مشاكل يجب حلها:\n\n")
            for issue in issues:
                self.log_text.insert(tk.END, f"  {issue}\n")
            self.log_text.insert(tk.END, "\n")

        if warnings:
            self.log_text.insert(tk.END, "ℹ️ معلومات النظام:\n\n")
            for warning in warnings:
                self.log_text.insert(tk.END, f"  {warning}\n")

        if not issues:
            self.log_text.insert(tk.END, "\n🎉 جميع المتطلبات متوفرة! يمكنك البدء في التحويل.\n")

        self.log_text.insert(tk.END, "\n" + "=" * 50 + "\n")
        self.log_text.see(tk.END)
        self.log_text.config(state='disabled')

        # تحديث حالة الزر
        if issues:
            self.update_status("توجد مشاكل في المتطلبات", COLORS['error'])
        else:
            self.update_status("جميع المتطلبات متوفرة", COLORS['success'])
    def start_conversion(self):
        """بدء عملية التحويل بدون نوافذ منبثقة"""
        if self.is_converting:
            self.add_to_log("⚠️ عملية تحويل جارية بالفعل")
            return

        path = self.selected_path.get()
        if not path:
            self.add_to_log("❌ خطأ: من فضلك اختر ملف أو مجلد أولاً")
            self.update_status("لم يتم اختيار ملف", COLORS['error'])
            return

        output_folder = self.output_folder_var.get()
        if not output_folder:
            # استخدام مجلد افتراضي بجانب الملف المصدر
            if os.path.isfile(path):
                output_folder = os.path.join(os.path.dirname(path), "dist")
            else:
                output_folder = os.path.join(path, "dist")

            # إنشاء المجلد إذا لم يكن موجوداً
            os.makedirs(output_folder, exist_ok=True)
            self.output_folder_var.set(output_folder)
            self.add_to_log(f"📁 استخدام مجلد الحفظ الافتراضي: {output_folder}")

        # بدء التحويل في thread منفصل
        self.is_converting = True
        self.convert_btn.configure(text="⏳ جاري التحويل...", state="disabled")
        self.progress_bar.start()
        self.update_status("جاري التحويل...", COLORS['warning'])

        # إضافة رسالة بداية التحويل
        self.add_to_log(f"🚀 بدء تحويل: {os.path.basename(path)}")

        thread = threading.Thread(target=self.conversion_worker, args=(path, output_folder))
        thread.daemon = True
        thread.start()

    def conversion_worker(self, path, output_folder):
        """عامل التحويل في thread منفصل"""
        try:
            if os.path.isfile(path):
                # تحويل ملف واحد
                self.convert_single_file(path, output_folder)
            else:
                # تحويل جميع ملفات .py في المجلد
                self.convert_folder(path, output_folder)

            # تسجيل النجاح
            self.log_conversion(path, success=True)

            # تحديث الواجهة
            self.root.after(0, self.conversion_success)

        except Exception as e:
            error_msg = str(e)
            self.log_conversion(path, success=False, error_msg=error_msg)

            # تحديث الواجهة
            self.root.after(0, lambda: self.conversion_error(error_msg))

    def convert_single_file(self, file_path, output_folder):
        """تحويل ملف واحد"""
        cmd_args = self.build_command_args(file_path)

        # تشغيل PyInstaller
        PyInstaller.__main__.run(cmd_args[1:])  # إزالة 'pyinstaller' من البداية

        # إضافة إلى قائمة الملفات المحولة
        filename = os.path.basename(file_path)
        timestamp = datetime.now().strftime("%H:%M:%S")
        self.root.after(0, lambda: self.add_to_converted_list(f"{filename} - {timestamp}"))

    def convert_folder(self, folder_path, output_folder):
        """تحويل جميع ملفات Python في المجلد"""
        py_files = [os.path.join(folder_path, f) for f in os.listdir(folder_path) if f.endswith('.py')]

        if not py_files:
            raise Exception("المجلد لا يحتوي على ملفات Python")

        for file_path in py_files:
            self.convert_single_file(file_path, output_folder)

    def conversion_success(self):
        """تحديث الواجهة عند نجاح التحويل بدون نوافذ منبثقة"""
        self.is_converting = False
        self.convert_btn.configure(text="🚀 بدء التحويل", state="normal")
        self.progress_bar.stop()
        self.update_status("تم التحويل بنجاح!", COLORS['success'])

        output_folder = self.output_folder_var.get()
        self.add_to_log(f"✅ تم التحويل بنجاح إلى: {output_folder}")
        self.add_to_log("🎉 يمكنك الآن العثور على الملف التنفيذي في مجلد الحفظ")

        # حفظ الإعدادات
        self.save_settings()

    def conversion_error(self, error_msg):
        """تحديث الواجهة عند فشل التحويل بدون نوافذ منبثقة"""
        self.is_converting = False
        self.convert_btn.configure(text="🚀 بدء التحويل", state="normal")
        self.progress_bar.stop()
        self.update_status("فشل التحويل", COLORS['error'])

        self.add_to_log(f"❌ فشل التحويل: {error_msg}")
        self.add_to_log("💡 تحقق من السجل أعلاه لمعرفة تفاصيل الخطأ")

    def log_conversion(self, file_path, success=True, error_msg=None):
        """تسجيل عملية التحويل"""
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")

        if success:
            message = f"[{timestamp}] ✅ تم تحويل {file_path} بنجاح"
            logging.info(f"تم تحويل {file_path} بنجاح")
        else:
            message = f"[{timestamp}] ❌ فشل تحويل {file_path}: {error_msg}"
            logging.error(f"فشل تحويل {file_path}: {error_msg}")

        # إضافة إلى سجل الواجهة
        self.root.after(0, lambda: self.add_to_log(message))

    def add_to_log(self, message):
        """إضافة رسالة إلى سجل الواجهة"""
        self.log_text.config(state='normal')
        self.log_text.insert(tk.END, message + "\n")
        self.log_text.see(tk.END)
        self.log_text.config(state='disabled')

    def add_to_converted_list(self, item):
        """إضافة عنصر إلى قائمة الملفات المحولة"""
        self.converted_listbox.insert(tk.END, item)
        self.converted_listbox.see(tk.END)

    def update_status(self, message, color=None):
        """تحديث رسالة الحالة"""
        if color is None:
            color = COLORS['text_primary']

        self.status_label.configure(text=message, fg=color)
        self.status_bar_label.configure(text=f"{message} | Python to EXE Converter Pro v2.0")

    def save_settings(self):
        """حفظ الإعدادات"""
        settings = {
            'last_path': self.selected_path.get(),
            'output_folder': self.output_folder_var.get(),
            'icon_path': self.icon_path.get(),
            'one_file': self.one_file.get(),
            'hide_console': self.hide_console.get(),
            'debug_mode': self.debug_mode.get(),
            'add_data': self.add_data.get(),
            'optimize': self.optimize.get(),
            'upx_compress': self.upx_compress.get()
        }

        try:
            with open('settings.json', 'w', encoding='utf-8') as f:
                json.dump(settings, f, ensure_ascii=False, indent=2)
        except Exception as e:
            print(f"خطأ في حفظ الإعدادات: {e}")

    def load_settings(self):
        """تحميل الإعدادات"""
        try:
            if os.path.exists('settings.json'):
                with open('settings.json', 'r', encoding='utf-8') as f:
                    settings = json.load(f)

                    self.selected_path.set(settings.get('last_path', ''))
                    self.output_folder_var.set(settings.get('output_folder', ''))
                    self.icon_path.set(settings.get('icon_path', ''))
                    self.one_file.set(settings.get('one_file', True))
                    self.hide_console.set(settings.get('hide_console', True))
                    self.debug_mode.set(settings.get('debug_mode', False))
                    self.add_data.set(settings.get('add_data', False))
                    self.optimize.set(settings.get('optimize', True))
                    self.upx_compress.set(settings.get('upx_compress', False))

                    # تحديث معاينة الأمر
                    self.update_command_preview()
        except Exception as e:
            print(f"خطأ في تحميل الإعدادات: {e}")

    def run(self):
        """تشغيل التطبيق"""
        # تحديث معاينة الأمر الأولية
        self.update_command_preview()

        # ربط أحداث الإغلاق
        self.root.protocol("WM_DELETE_WINDOW", self.on_closing)

        # تشغيل الحلقة الرئيسية
        self.root.mainloop()

    def on_closing(self):
        """عند إغلاق التطبيق بدون نوافذ منبثقة"""
        if self.is_converting:
            # إيقاف التحويل وحفظ الإعدادات
            self.add_to_log("⚠️ إغلاق التطبيق أثناء التحويل...")
            self.is_converting = False
            self.progress_bar.stop()

        self.save_settings()
        self.root.destroy()

# تشغيل التطبيق
if __name__ == "__main__":
    app = PyInstallerGUI()
    app.run()
