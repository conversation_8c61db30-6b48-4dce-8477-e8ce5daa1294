2025-07-15 14:34:40,271 - INFO - PyInstaller: 6.14.2, contrib hooks: 2025.5
2025-07-15 14:34:40,271 - INFO - Python: 3.11.0
2025-07-15 14:34:40,276 - INFO - Platform: Windows-10-10.0.26100-SP0
2025-07-15 14:34:40,276 - INFO - Python environment: C:\Users\<USER>\Desktop\تحويل ملفا py  الى  exe\pyenv311
2025-07-15 14:34:40,277 - INFO - wrote C:\Users\<USER>\Desktop\تحويل ملفا py  الى  exe\python.spec
2025-07-15 14:34:40,279 - INFO - Module search paths (PYTHONPATH):
['c:\\Users\\<USER>\\Desktop\\تحويل ملفا py  الى  exe',
 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\python311.zip',
 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\DLLs',
 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib',
 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311',
 'C:\\Users\\<USER>\\Desktop\\تحويل ملفا py  الى  exe\\pyenv311',
 'C:\\Users\\<USER>\\Desktop\\تحويل ملفا py  الى  '
 'exe\\pyenv311\\Lib\\site-packages',
 'C:\\Users\\<USER>\\Desktop\\تحويل ملفا py  الى  '
 'exe\\pyenv311\\Lib\\site-packages\\win32',
 'C:\\Users\\<USER>\\Desktop\\تحويل ملفا py  الى  '
 'exe\\pyenv311\\Lib\\site-packages\\win32\\lib',
 'C:\\Users\\<USER>\\Desktop\\تحويل ملفا py  الى  '
 'exe\\pyenv311\\Lib\\site-packages\\Pythonwin',
 'C:\\Users\\<USER>\\Desktop']
2025-07-15 14:34:40,512 - INFO - checking Analysis
2025-07-15 14:34:40,512 - INFO - Building Analysis because Analysis-00.toc is non existent
2025-07-15 14:34:40,512 - INFO - Running Analysis Analysis-00.toc
2025-07-15 14:34:40,512 - INFO - Target bytecode optimization level: 2
2025-07-15 14:34:40,512 - INFO - Initializing module dependency graph...
2025-07-15 14:34:40,513 - INFO - Initializing module graph hook caches...
2025-07-15 14:34:40,524 - INFO - Analyzing modules for base_library.zip ...
2025-07-15 14:34:41,216 - INFO - Processing standard module hook 'hook-encodings.py' from 'C:\\Users\\<USER>\\Desktop\\تحويل ملفا py  الى  exe\\pyenv311\\Lib\\site-packages\\PyInstaller\\hooks'
2025-07-15 14:34:41,756 - INFO - Processing standard module hook 'hook-heapq.py' from 'C:\\Users\\<USER>\\Desktop\\تحويل ملفا py  الى  exe\\pyenv311\\Lib\\site-packages\\PyInstaller\\hooks'
2025-07-15 14:34:42,895 - INFO - Processing standard module hook 'hook-pickle.py' from 'C:\\Users\\<USER>\\Desktop\\تحويل ملفا py  الى  exe\\pyenv311\\Lib\\site-packages\\PyInstaller\\hooks'
2025-07-15 14:34:45,117 - INFO - Caching module dependency graph...
2025-07-15 14:34:45,140 - INFO - Looking for Python shared library...
2025-07-15 14:34:45,144 - INFO - Using Python shared library: C:\Users\<USER>\AppData\Local\Programs\Python\Python311\python311.dll
2025-07-15 14:34:45,144 - INFO - Analyzing C:\Users\<USER>\Desktop\python.py
2025-07-15 14:34:45,145 - INFO - Processing pre-find-module-path hook 'hook-tkinter.py' from 'C:\\Users\\<USER>\\Desktop\\تحويل ملفا py  الى  exe\\pyenv311\\Lib\\site-packages\\PyInstaller\\hooks\\pre_find_module_path'
2025-07-15 14:34:45,146 - INFO - TclTkInfo: initializing cached Tcl/Tk info...
2025-07-15 14:34:45,363 - INFO - Processing standard module hook 'hook-_tkinter.py' from 'C:\\Users\\<USER>\\Desktop\\تحويل ملفا py  الى  exe\\pyenv311\\Lib\\site-packages\\PyInstaller\\hooks'
2025-07-15 14:34:45,389 - INFO - Processing module hooks (post-graph stage)...
2025-07-15 14:34:45,391 - INFO - Processing standard module hook 'hook-_tkinter.py' from 'C:\\Users\\<USER>\\Desktop\\تحويل ملفا py  الى  exe\\pyenv311\\Lib\\site-packages\\PyInstaller\\hooks'
2025-07-15 14:34:45,397 - INFO - Performing binary vs. data reclassification (923 entries)
2025-07-15 14:34:49,217 - INFO - Looking for ctypes DLLs
2025-07-15 14:34:49,223 - INFO - Analyzing run-time hooks ...
2025-07-15 14:34:49,224 - INFO - Including run-time hook 'pyi_rth_inspect.py' from 'C:\\Users\\<USER>\\Desktop\\تحويل ملفا py  الى  exe\\pyenv311\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks'
2025-07-15 14:34:49,226 - INFO - Including run-time hook 'pyi_rth__tkinter.py' from 'C:\\Users\\<USER>\\Desktop\\تحويل ملفا py  الى  exe\\pyenv311\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks'
2025-07-15 14:34:49,242 - INFO - Creating base_library.zip...
2025-07-15 14:34:49,396 - INFO - Looking for dynamic libraries
2025-07-15 14:34:49,485 - INFO - Extra DLL search directories (AddDllDirectory): []
2025-07-15 14:34:49,485 - INFO - Extra DLL search directories (PATH): []
2025-07-15 14:34:49,617 - INFO - Warnings written to C:\Users\<USER>\Desktop\تحويل ملفا py  الى  exe\build\python\warn-python.txt
2025-07-15 14:34:49,628 - INFO - Graph cross-reference written to C:\Users\<USER>\Desktop\تحويل ملفا py  الى  exe\build\python\xref-python.html
2025-07-15 14:34:49,657 - INFO - checking PYZ
2025-07-15 14:34:49,657 - INFO - Building PYZ because PYZ-00.toc is non existent
2025-07-15 14:34:49,657 - INFO - Building PYZ (ZlibArchive) C:\Users\<USER>\Desktop\تحويل ملفا py  الى  exe\build\python\PYZ-00.pyz
2025-07-15 14:34:50,065 - INFO - Building PYZ (ZlibArchive) C:\Users\<USER>\Desktop\تحويل ملفا py  الى  exe\build\python\PYZ-00.pyz completed successfully.
2025-07-15 14:34:50,081 - INFO - checking PKG
2025-07-15 14:34:50,081 - INFO - Building PKG because PKG-00.toc is non existent
2025-07-15 14:34:50,081 - INFO - Building PKG (CArchive) python.pkg
2025-07-15 14:34:52,072 - INFO - Building PKG (CArchive) python.pkg completed successfully.
2025-07-15 14:34:52,085 - INFO - Bootloader C:\Users\<USER>\Desktop\تحويل ملفا py  الى  exe\pyenv311\Lib\site-packages\PyInstaller\bootloader\Windows-64bit-intel\runw.exe
2025-07-15 14:34:52,085 - INFO - checking EXE
2025-07-15 14:34:52,085 - INFO - Building EXE because EXE-00.toc is non existent
2025-07-15 14:34:52,085 - INFO - Building EXE from EXE-00.toc
2025-07-15 14:34:52,085 - INFO - Copying bootloader EXE to C:\Users\<USER>\Desktop\python.exe
2025-07-15 14:34:52,088 - INFO - Copying icon to EXE
2025-07-15 14:34:52,089 - INFO - Copying 0 resources to EXE
2025-07-15 14:34:52,089 - INFO - Embedding manifest in EXE
2025-07-15 14:34:52,091 - INFO - Appending PKG archive to EXE
2025-07-15 14:34:52,095 - INFO - Fixing EXE headers
2025-07-15 14:34:52,152 - INFO - Building EXE from EXE-00.toc completed successfully.
2025-07-15 14:34:52,165 - INFO - Build complete! The results are available in: C:\Users\<USER>\Desktop
2025-07-15 14:34:52,167 - INFO - تم تحويل C:/Users/<USER>/Desktop/python.py بنجاح
